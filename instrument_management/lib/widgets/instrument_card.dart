import 'package:flutter/material.dart';
import '../models/instrument.dart';

class InstrumentCard extends StatelessWidget {
  final Instrument instrument;
  final VoidCallback onTap;

  const InstrumentCard({
    super.key,
    required this.instrument,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 仪器名称和状态
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      instrument.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  _buildStatusBadge(instrument.status),
                ],
              ),
              const SizedBox(height: 8),

              // 型号和制造商
              Text(
                '${instrument.manufacturer} ${instrument.model}',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 8),

              // 位置
              Row(
                children: [
                  const Icon(
                    Icons.location_on,
                    size: 16,
                    color: Colors.grey,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    instrument.location,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // 特性标签
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: [
                  if (instrument.requiresTraining)
                    _buildFeatureChip('需培训', Colors.orange),
                  if (instrument.acceptsSampleSubmission)
                    _buildFeatureChip('接受送样', Colors.blue),
                  if (instrument.controlType == InstrumentControlType.automated)
                    _buildFeatureChip('自动化', Colors.green),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(InstrumentStatus status) {
    Color color;
    String text;

    switch (status) {
      case InstrumentStatus.online:
        color = Colors.green;
        text = '在线';
        break;
      case InstrumentStatus.inUse:
        color = Colors.blue;
        text = '使用中';
        break;
      case InstrumentStatus.maintenance:
        color = Colors.orange;
        text = '维护中';
        break;
      case InstrumentStatus.fault:
        color = Colors.red;
        text = '故障';
        break;
      case InstrumentStatus.offline:
        color = Colors.grey;
        text = '离线';
        break;
      case InstrumentStatus.scrapped:
        color = Colors.black;
        text = '已报废';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildFeatureChip(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}













