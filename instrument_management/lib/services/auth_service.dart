import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';
import 'api_service.dart';

/// 认证服务
class AuthService {
  static const String _tokenKey = 'auth_token';
  static const String _userKey = 'user_data';
  static const String _refreshTokenKey = 'refresh_token';

  final ApiService _apiService;
  final SharedPreferences _prefs;

  AuthService(this._apiService, this._prefs);

  /// 统一身份认证登录
  Future<AuthResult> loginWithSSO(String username, String password) async {
    try {
      final response = await _apiService.post('/auth/sso/login', body: {
        'username': username,
        'password': password,
      });

      final token = response['token'] as String;
      final refreshToken = response['refresh_token'] as String;
      final userJson = response['user'] as Map<String, dynamic>;
      final user = User.fromJson(userJson);

      // 保存认证信息
      await _saveAuthData(token, refreshToken, user);

      return AuthResult.success(user);
    } catch (e) {
      return AuthResult.error(e.toString());
    }
  }

  /// 学生注册（需要邮箱和电话）
  Future<AuthResult> registerStudent({
    required String username,
    required String email,
    required String phone,
    required String researchGroupId,
  }) async {
    try {
      final response = await _apiService.post('/auth/register/student', body: {
        'username': username,
        'email': email,
        'phone': phone,
        'research_group_id': researchGroupId,
      });

      return AuthResult.success(null, message: '注册成功，请等待导师激活');
    } catch (e) {
      return AuthResult.error(e.toString());
    }
  }

  /// 教师注册
  Future<AuthResult> registerTeacher({
    required String username,
    required String email,
    required String phone,
    required String fullName,
    required String department,
  }) async {
    try {
      final response = await _apiService.post('/auth/register/teacher', body: {
        'username': username,
        'email': email,
        'phone': phone,
        'full_name': fullName,
        'department': department,
      });

      final token = response['token'] as String;
      final refreshToken = response['refresh_token'] as String;
      final userJson = response['user'] as Map<String, dynamic>;
      final user = User.fromJson(userJson);

      // 保存认证信息
      await _saveAuthData(token, refreshToken, user);

      return AuthResult.success(user);
    } catch (e) {
      return AuthResult.error(e.toString());
    }
  }

  /// 学生激活（由导师操作）
  Future<AuthResult> activateStudent(String studentId) async {
    try {
      final token = await getToken();
      if (token == null) {
        return AuthResult.error('未登录');
      }

      await _apiService.post(
        '/auth/activate/student/$studentId',
        headers: _apiService.getAuthHeaders(token),
      );

      return AuthResult.success(null, message: '学生激活成功');
    } catch (e) {
      return AuthResult.error(e.toString());
    }
  }

  /// 获取当前用户信息
  Future<User?> getCurrentUser() async {
    final userJson = _prefs.getString(_userKey);
    if (userJson != null) {
      try {
        final userMap = jsonDecode(userJson) as Map<String, dynamic>;
        return User.fromJson(userMap);
      } catch (e) {
        // 数据损坏，清除缓存
        await _clearAuthData();
        return null;
      }
    }
    return null;
  }

  /// 获取认证令牌
  Future<String?> getToken() async {
    return _prefs.getString(_tokenKey);
  }

  /// 获取刷新令牌
  Future<String?> getRefreshToken() async {
    return _prefs.getString(_refreshTokenKey);
  }

  /// 刷新令牌
  Future<AuthResult> refreshToken() async {
    try {
      final refreshToken = await getRefreshToken();
      if (refreshToken == null) {
        return AuthResult.error('刷新令牌不存在');
      }

      final response = await _apiService.post('/auth/refresh', body: {
        'refresh_token': refreshToken,
      });

      final newToken = response['token'] as String;
      final newRefreshToken = response['refresh_token'] as String;

      // 更新令牌
      await _prefs.setString(_tokenKey, newToken);
      await _prefs.setString(_refreshTokenKey, newRefreshToken);

      return AuthResult.success(null, message: '令牌刷新成功');
    } catch (e) {
      return AuthResult.error(e.toString());
    }
  }

  /// 登出
  Future<void> logout() async {
    try {
      final token = await getToken();
      if (token != null) {
        // 通知服务器登出
        await _apiService.post(
          '/auth/logout',
          headers: _apiService.getAuthHeaders(token),
        );
      }
    } catch (e) {
      // 忽略服务器登出错误
    } finally {
      // 清除本地认证数据
      await _clearAuthData();
    }
  }

  /// 检查登录状态
  Future<bool> isLoggedIn() async {
    final token = await getToken();
    final user = await getCurrentUser();
    return token != null && user != null;
  }

  /// 更新用户信息
  Future<AuthResult> updateProfile({
    String? fullName,
    String? department,
    String? avatarUrl,
  }) async {
    try {
      final token = await getToken();
      if (token == null) {
        return AuthResult.error('未登录');
      }

      final updateData = <String, dynamic>{};
      if (fullName != null) updateData['full_name'] = fullName;
      if (department != null) updateData['department'] = department;
      if (avatarUrl != null) updateData['avatar_url'] = avatarUrl;

      final response = await _apiService.put(
        '/auth/profile',
        body: updateData,
        headers: _apiService.getAuthHeaders(token),
      );

      final userJson = response['user'] as Map<String, dynamic>;
      final updatedUser = User.fromJson(userJson);

      // 更新本地用户信息
      await _saveUserData(updatedUser);

      return AuthResult.success(updatedUser);
    } catch (e) {
      return AuthResult.error(e.toString());
    }
  }

  /// 保存认证数据到本地存储
  Future<void> _saveAuthData(String token, String refreshToken, User user) async {
    await _prefs.setString(_tokenKey, token);
    await _prefs.setString(_refreshTokenKey, refreshToken);
    await _saveUserData(user);
  }

  /// 保存用户信息
  Future<void> _saveUserData(User user) async {
    final userJson = jsonEncode(user.toJson());
    await _prefs.setString(_userKey, userJson);
  }

  /// 清除认证数据
  Future<void> _clearAuthData() async {
    await _prefs.remove(_tokenKey);
    await _prefs.remove(_refreshTokenKey);
    await _prefs.remove(_userKey);
  }
}

/// 认证结果类
class AuthResult {
  final bool success;
  final User? user;
  final String? message;
  final String? error;

  const AuthResult._({
    required this.success,
    this.user,
    this.message,
    this.error,
  });

  factory AuthResult.success(User? user, {String? message}) {
    return AuthResult._(
      success: true,
      user: user,
      message: message,
    );
  }

  factory AuthResult.error(String error) {
    return AuthResult._(
      success: false,
      error: error,
    );
  }
}













