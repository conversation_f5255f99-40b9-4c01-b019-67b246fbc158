import 'dart:convert';
import 'package:http/http.dart' as http;

/// API服务基础类
class ApiService {
  static const String baseUrl = 'http://localhost:8080/api/v1'; // 开发环境地址
  static const Duration timeout = Duration(seconds: 30);

  final http.Client _client;

  ApiService() : _client = http.Client();

  /// GET请求
  Future<Map<String, dynamic>> get(String endpoint, {Map<String, String>? headers}) async {
    try {
      final url = Uri.parse('$baseUrl$endpoint');
      final response = await _client.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          ...?headers,
        },
      ).timeout(timeout);

      return _handleResponse(response);
    } catch (e) {
      throw ApiException('网络请求失败: $e');
    }
  }

  /// POST请求
  Future<Map<String, dynamic>> post(
    String endpoint,
    {Map<String, dynamic>? body, Map<String, String>? headers}
  ) async {
    try {
      final url = Uri.parse('$baseUrl$endpoint');
      final response = await _client.post(
        url,
        headers: {
          'Content-Type': 'application/json',
          ...?headers,
        },
        body: body != null ? jsonEncode(body) : null,
      ).timeout(timeout);

      return _handleResponse(response);
    } catch (e) {
      throw ApiException('网络请求失败: $e');
    }
  }

  /// PUT请求
  Future<Map<String, dynamic>> put(
    String endpoint,
    {Map<String, dynamic>? body, Map<String, String>? headers}
  ) async {
    try {
      final url = Uri.parse('$baseUrl$endpoint');
      final response = await _client.put(
        url,
        headers: {
          'Content-Type': 'application/json',
          ...?headers,
        },
        body: body != null ? jsonEncode(body) : null,
      ).timeout(timeout);

      return _handleResponse(response);
    } catch (e) {
      throw ApiException('网络请求失败: $e');
    }
  }

  /// DELETE请求
  Future<Map<String, dynamic>> delete(String endpoint, {Map<String, String>? headers}) async {
    try {
      final url = Uri.parse('$baseUrl$endpoint');
      final response = await _client.delete(
        url,
        headers: {
          'Content-Type': 'application/json',
          ...?headers,
        },
      ).timeout(timeout);

      return _handleResponse(response);
    } catch (e) {
      throw ApiException('网络请求失败: $e');
    }
  }

  /// 处理HTTP响应
  Map<String, dynamic> _handleResponse(http.Response response) {
    final statusCode = response.statusCode;
    final body = response.body;

    if (statusCode >= 200 && statusCode < 300) {
      if (body.isEmpty) {
        return {};
      }
      try {
        return jsonDecode(body) as Map<String, dynamic>;
      } catch (e) {
        throw ApiException('响应数据解析失败: $e');
      }
    } else {
      String errorMessage = '请求失败';
      if (body.isNotEmpty) {
        try {
          final errorData = jsonDecode(body) as Map<String, dynamic>;
          errorMessage = errorData['message'] as String? ?? errorMessage;
        } catch (_) {
          errorMessage = body;
        }
      }
      throw ApiException(errorMessage, statusCode: statusCode);
    }
  }

  /// 设置认证头
  Map<String, String> getAuthHeaders(String token) {
    return {
      'Authorization': 'Bearer $token',
    };
  }

  void dispose() {
    _client.close();
  }
}

/// API异常类
class ApiException implements Exception {
  final String message;
  final int? statusCode;

  const ApiException(this.message, {this.statusCode});

  @override
  String toString() {
    if (statusCode != null) {
      return 'ApiException: $message (HTTP $statusCode)';
    }
    return 'ApiException: $message';
  }
}

/// API响应包装类
class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? message;
  final int? statusCode;

  const ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.statusCode,
  });

  factory ApiResponse.success(T data, {String? message}) {
    return ApiResponse(
      success: true,
      data: data,
      message: message,
    );
  }

  factory ApiResponse.error(String message, {int? statusCode}) {
    return ApiResponse(
      success: false,
      message: message,
      statusCode: statusCode,
    );
  }

  factory ApiResponse.fromJson(Map<String, dynamic> json, T Function(Map<String, dynamic>) fromJson) {
    final success = json['success'] as bool? ?? false;
    final message = json['message'] as String?;

    if (success) {
      final dataJson = json['data'];
      if (dataJson != null) {
        return ApiResponse.success(fromJson(dataJson as Map<String, dynamic>), message: message);
      }
      return ApiResponse.success(null as T, message: message);
    } else {
      return ApiResponse.error(message ?? '未知错误');
    }
  }
}

/// 分页响应
class PaginatedResponse<T> {
  final List<T> items;
  final int totalCount;
  final int page;
  final int pageSize;
  final bool hasNextPage;
  final bool hasPreviousPage;

  const PaginatedResponse({
    required this.items,
    required this.totalCount,
    required this.page,
    required this.pageSize,
    required this.hasNextPage,
    required this.hasPreviousPage,
  });

  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    return PaginatedResponse(
      items: (json['items'] as List<dynamic>?)
          ?.map((item) => fromJson(item as Map<String, dynamic>))
          .toList() ?? [],
      totalCount: json['total_count'] as int? ?? 0,
      page: json['page'] as int? ?? 1,
      pageSize: json['page_size'] as int? ?? 10,
      hasNextPage: json['has_next_page'] as bool? ?? false,
      hasPreviousPage: json['has_previous_page'] as bool? ?? false,
    );
  }
}













