import 'package:flutter/material.dart';
import '../../models/instrument.dart';
import '../../widgets/instrument_card.dart';

class InstrumentListScreen extends StatefulWidget {
  const InstrumentListScreen({super.key});

  @override
  State<InstrumentListScreen> createState() => _InstrumentListScreenState();
}

class _InstrumentListScreenState extends State<InstrumentListScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<String> _categories = ['全部', '光学仪器', '电子仪器', '分析仪器', '其他'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _categories.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('仪器列表'),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: _categories.map((category) => Tab(text: category)).toList(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              // TODO: 筛选功能
            },
          ),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // TODO: 搜索功能
            },
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: _categories.map((category) => _buildInstrumentList(category)).toList(),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // TODO: 添加新仪器（管理员功能）
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildInstrumentList(String category) {
    // 这里应该从API获取数据，暂时使用模拟数据
    final instruments = _getMockInstruments(category);

    return RefreshIndicator(
      onRefresh: () async {
        // TODO: 刷新数据
      },
      child: instruments.isEmpty
          ? const Center(
              child: Text(
                '暂无仪器',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: instruments.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: InstrumentCard(
                    instrument: instruments[index],
                    onTap: () {
                      // TODO: 跳转到仪器详情页面
                    },
                  ),
                );
              },
            ),
    );
  }

  List<Instrument> _getMockInstruments(String category) {
    // 模拟数据
    return [
      Instrument(
        id: '1',
        name: '扫描电子显微镜',
        description: '高分辨率扫描电子显微镜，用于材料微观结构分析',
        model: 'SEM-1000',
        manufacturer: 'NPU Instruments',
        location: '理学院B101',
        status: InstrumentStatus.online,
        controlType: InstrumentControlType.computer,
        requiresTraining: true,
        acceptsSampleSubmission: false,
        requiresReservation: true,
        responsibleUserId: 'user1',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
        workingHours: [],
        billingConfig: null,
        userTags: [],
      ),
      Instrument(
        id: '2',
        name: 'X射线衍射仪',
        description: '用于晶体结构分析的X射线衍射仪',
        model: 'XRD-2000',
        manufacturer: 'NPU Instruments',
        location: '材料学院A203',
        status: InstrumentStatus.inUse,
        controlType: InstrumentControlType.automated,
        requiresTraining: true,
        acceptsSampleSubmission: true,
        requiresReservation: true,
        responsibleUserId: 'user2',
        createdAt: DateTime.now().subtract(const Duration(days: 25)),
        updatedAt: DateTime.now(),
        workingHours: [],
        billingConfig: null,
        userTags: [],
      ),
      Instrument(
        id: '3',
        name: '原子力显微镜',
        description: '纳米级表面形貌分析仪器',
        model: 'AFM-300',
        manufacturer: 'NPU Instruments',
        location: '物理学院C105',
        status: InstrumentStatus.maintenance,
        controlType: InstrumentControlType.manual,
        requiresTraining: false,
        acceptsSampleSubmission: false,
        requiresReservation: true,
        responsibleUserId: 'user3',
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
        updatedAt: DateTime.now(),
        workingHours: [],
        billingConfig: null,
        userTags: [],
      ),
    ];
  }
}













