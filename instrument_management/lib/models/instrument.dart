/// 仪器模型
class Instrument {
  final String id;
  final String name;
  final String? description;
  final String model;
  final String manufacturer;
  final String location;
  final String? imageUrl;
  final InstrumentStatus status;
  final InstrumentControlType controlType;
  final bool requiresTraining;
  final bool acceptsSampleSubmission;
  final bool requiresReservation;
  final List<WorkingHours> workingHours;
  final BillingConfig billingConfig;
  final List<UserTag> userTags;
  final String responsibleUserId;
  final DateTime? lastMaintenanceDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Instrument({
    required this.id,
    required this.name,
    this.description,
    required this.model,
    required this.manufacturer,
    required this.location,
    this.imageUrl,
    required this.status,
    required this.controlType,
    required this.requiresTraining,
    required this.acceptsSampleSubmission,
    required this.requiresReservation,
    required this.workingHours,
    required this.billingConfig,
    required this.userTags,
    required this.responsibleUserId,
    this.lastMaintenanceDate,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Instrument.fromJson(Map<String, dynamic> json) {
    return Instrument(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      model: json['model'] as String,
      manufacturer: json['manufacturer'] as String,
      location: json['location'] as String,
      imageUrl: json['image_url'] as String?,
      status: InstrumentStatus.values.firstWhere(
        (status) => status.toString().split('.').last == json['status'],
        orElse: () => InstrumentStatus.offline,
      ),
      controlType: InstrumentControlType.values.firstWhere(
        (type) => type.toString().split('.').last == json['control_type'],
        orElse: () => InstrumentControlType.manual,
      ),
      requiresTraining: json['requires_training'] as bool? ?? false,
      acceptsSampleSubmission:
          json['accepts_sample_submission'] as bool? ?? false,
      requiresReservation: json['requires_reservation'] as bool? ?? false,
      workingHours: (json['working_hours'] as List<dynamic>?)
              ?.map(
                  (item) => WorkingHours.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
      billingConfig: BillingConfig.fromJson(
          json['billing_config'] as Map<String, dynamic>),
      userTags: (json['user_tags'] as List<dynamic>?)
              ?.map((item) => UserTag.fromJson(item as Map<String, dynamic>))
              .toList() ??
          [],
      responsibleUserId: json['responsible_user_id'] as String,
      lastMaintenanceDate: json['last_maintenance_date'] != null
          ? DateTime.parse(json['last_maintenance_date'] as String)
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'model': model,
      'manufacturer': manufacturer,
      'location': location,
      'image_url': imageUrl,
      'status': status.toString().split('.').last,
      'control_type': controlType.toString().split('.').last,
      'requires_training': requiresTraining,
      'accepts_sample_submission': acceptsSampleSubmission,
      'requires_reservation': requiresReservation,
      'working_hours': workingHours.map((hours) => hours.toJson()).toList(),
      'billing_config': billingConfig.toJson(),
      'user_tags': userTags.map((tag) => tag.toJson()).toList(),
      'responsible_user_id': responsibleUserId,
      'last_maintenance_date': lastMaintenanceDate?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

/// 仪器状态枚举
enum InstrumentStatus {
  online, // 在线可用
  offline, // 离线
  inUse, // 使用中
  maintenance, // 维护中
  fault, // 故障
  scrapped, // 已报废
}

/// 仪器控制类型枚举
enum InstrumentControlType {
  manual, // 手动控制
  power, // 电源控制
  computer, // 电脑登录控制
  automated, // 自动化控制
}

/// 工作时间配置
class WorkingHours {
  final int dayOfWeek; // 1-7 (周一到周日)
  final String startTime; // HH:mm格式
  final String endTime; // HH:mm格式
  final bool isWorkingDay;

  const WorkingHours({
    required this.dayOfWeek,
    required this.startTime,
    required this.endTime,
    required this.isWorkingDay,
  });

  factory WorkingHours.fromJson(Map<String, dynamic> json) {
    return WorkingHours(
      dayOfWeek: json['day_of_week'] as int,
      startTime: json['start_time'] as String,
      endTime: json['end_time'] as String,
      isWorkingDay: json['is_working_day'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'day_of_week': dayOfWeek,
      'start_time': startTime,
      'end_time': endTime,
      'is_working_day': isWorkingDay,
    };
  }
}

/// 计费配置
class BillingConfig {
  final BillingType billingType;
  final double baseRate; // 基础费率
  final String unit; // 计费单位
  final Map<String, double> tagDiscounts; // 用户标签折扣

  const BillingConfig({
    required this.billingType,
    required this.baseRate,
    required this.unit,
    required this.tagDiscounts,
  });

  factory BillingConfig.fromJson(Map<String, dynamic> json) {
    return BillingConfig(
      billingType: BillingType.values.firstWhere(
        (type) => type.toString().split('.').last == json['billing_type'],
        orElse: () => BillingType.perHour,
      ),
      baseRate: (json['base_rate'] as num).toDouble(),
      unit: json['unit'] as String,
      tagDiscounts: Map<String, double>.from(json['tag_discounts'] as Map),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'billing_type': billingType.toString().split('.').last,
      'base_rate': baseRate,
      'unit': unit,
      'tag_discounts': tagDiscounts,
    };
  }
}

/// 计费类型枚举
enum BillingType {
  perHour, // 按小时计费
  perSample, // 按样品计费
  flatRate, // 固定费率
}

/// 用户标签
class UserTag {
  final String id;
  final String name;
  final String? description;
  final double priority; // 优先级权重
  final double discount; // 折扣比例 (0.0-1.0)

  const UserTag({
    required this.id,
    required this.name,
    this.description,
    required this.priority,
    required this.discount,
  });

  factory UserTag.fromJson(Map<String, dynamic> json) {
    return UserTag(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      priority: (json['priority'] as num).toDouble(),
      discount: (json['discount'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'priority': priority,
      'discount': discount,
    };
  }
}
