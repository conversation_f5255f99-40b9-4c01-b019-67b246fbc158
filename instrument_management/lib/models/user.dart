/// 用户模型
class User {
  final String id;
  final String username;
  final String email;
  final String phone;
  final UserRole role;
  final UserStatus status;
  final String? avatarUrl;
  final String? fullName;
  final String? department;
  final String? researchGroup;
  final DateTime? activatedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  const User({
    required this.id,
    required this.username,
    required this.email,
    required this.phone,
    required this.role,
    required this.status,
    this.avatarUrl,
    this.fullName,
    this.department,
    this.researchGroup,
    this.activatedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as String,
      username: json['username'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String,
      role: UserRole.values.firstWhere(
        (role) => role.toString().split('.').last == json['role'],
        orElse: () => UserRole.student,
      ),
      status: UserStatus.values.firstWhere(
        (status) => status.toString().split('.').last == json['status'],
        orElse: () => UserStatus.pending,
      ),
      avatarUrl: json['avatar_url'] as String?,
      fullName: json['full_name'] as String?,
      department: json['department'] as String?,
      researchGroup: json['research_group'] as String?,
      activatedAt: json['activated_at'] != null
          ? DateTime.parse(json['activated_at'] as String)
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'phone': phone,
      'role': role.toString().split('.').last,
      'status': status.toString().split('.').last,
      'avatar_url': avatarUrl,
      'full_name': fullName,
      'department': department,
      'research_group': researchGroup,
      'activated_at': activatedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  User copyWith({
    String? id,
    String? username,
    String? email,
    String? phone,
    UserRole? role,
    UserStatus? status,
    String? avatarUrl,
    String? fullName,
    String? department,
    String? researchGroup,
    DateTime? activatedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      role: role ?? this.role,
      status: status ?? this.status,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      fullName: fullName ?? this.fullName,
      department: department ?? this.department,
      researchGroup: researchGroup ?? this.researchGroup,
      activatedAt: activatedAt ?? this.activatedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// 用户角色枚举
enum UserRole {
  student, // 学生
  teacher, // 教师/研究员
  technician, // 仪器负责人
  admin, // 系统管理员
}

/// 用户状态枚举
enum UserStatus {
  pending, // 待激活
  active, // 已激活
  inactive, // 未激活
  suspended, // 已暂停
}

/// 课题组模型
class ResearchGroup {
  final String id;
  final String name;
  final String? description;
  final String leaderId;
  final List<String> memberIds;
  final double balance;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ResearchGroup({
    required this.id,
    required this.name,
    this.description,
    required this.leaderId,
    required this.memberIds,
    required this.balance,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ResearchGroup.fromJson(Map<String, dynamic> json) {
    return ResearchGroup(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      leaderId: json['leader_id'] as String,
      memberIds: List<String>.from(json['member_ids'] as List),
      balance: (json['balance'] as num).toDouble(),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'leader_id': leaderId,
      'member_ids': memberIds,
      'balance': balance,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}
