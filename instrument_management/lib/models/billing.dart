/// 财务交易模型
class Transaction {
  final String id;
  final String userId;
  final String researchGroupId;
  final TransactionType type;
  final double amount;
  final String description;
  final String? relatedReservationId;
  final String? relatedInstrumentId;
  final PaymentStatus status;
  final DateTime transactionDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Transaction({
    required this.id,
    required this.userId,
    required this.researchGroupId,
    required this.type,
    required this.amount,
    required this.description,
    this.relatedReservationId,
    this.relatedInstrumentId,
    required this.status,
    required this.transactionDate,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Transaction.fromJson(Map<String, dynamic> json) {
    return Transaction(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      researchGroupId: json['research_group_id'] as String,
      type: TransactionType.values.firstWhere(
        (type) => type.toString().split('.').last == json['type'],
        orElse: () => TransactionType.charge,
      ),
      amount: (json['amount'] as num).toDouble(),
      description: json['description'] as String,
      relatedReservationId: json['related_reservation_id'] as String?,
      relatedInstrumentId: json['related_instrument_id'] as String?,
      status: PaymentStatus.values.firstWhere(
        (status) => status.toString().split('.').last == json['status'],
        orElse: () => PaymentStatus.pending,
      ),
      transactionDate: DateTime.parse(json['transaction_date'] as String),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'research_group_id': researchGroupId,
      'type': type.toString().split('.').last,
      'amount': amount,
      'description': description,
      'related_reservation_id': relatedReservationId,
      'related_instrument_id': relatedInstrumentId,
      'status': status.toString().split('.').last,
      'transaction_date': transactionDate.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

/// 交易类型枚举
enum TransactionType {
  charge, // 收费
  refund, // 退款
  deposit, // 充值
  adjustment, // 调整
}

/// 支付状态枚举
enum PaymentStatus {
  pending, // 待支付
  completed, // 已完成
  failed, // 失败
  cancelled, // 已取消
  refunded, // 已退款
}

/// 账单模型
class Bill {
  final String id;
  final String researchGroupId;
  final String title;
  final String description;
  final double totalAmount;
  final List<BillItem> items;
  final BillStatus status;
  final DateTime billDate;
  final DateTime? dueDate;
  final DateTime? paidAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Bill({
    required this.id,
    required this.researchGroupId,
    required this.title,
    required this.description,
    required this.totalAmount,
    required this.items,
    required this.status,
    required this.billDate,
    this.dueDate,
    this.paidAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Bill.fromJson(Map<String, dynamic> json) {
    return Bill(
      id: json['id'] as String,
      researchGroupId: json['research_group_id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      totalAmount: (json['total_amount'] as num).toDouble(),
      items: (json['items'] as List<dynamic>)
          .map((item) => BillItem.fromJson(item as Map<String, dynamic>))
          .toList(),
      status: BillStatus.values.firstWhere(
        (status) => status.toString().split('.').last == json['status'],
        orElse: () => BillStatus.unpaid,
      ),
      billDate: DateTime.parse(json['bill_date'] as String),
      dueDate: json['due_date'] != null
          ? DateTime.parse(json['due_date'] as String)
          : null,
      paidAt: json['paid_at'] != null
          ? DateTime.parse(json['paid_at'] as String)
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'research_group_id': researchGroupId,
      'title': title,
      'description': description,
      'total_amount': totalAmount,
      'items': items.map((item) => item.toJson()).toList(),
      'status': status.toString().split('.').last,
      'bill_date': billDate.toIso8601String(),
      'due_date': dueDate?.toIso8601String(),
      'paid_at': paidAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

/// 账单项目
class BillItem {
  final String id;
  final String instrumentName;
  final String reservationId;
  final DateTime usageDate;
  final double duration; // 使用时长（小时）
  final double unitPrice; // 单价
  final double quantity; // 数量（样品数或小时数）
  final double amount; // 金额
  final double discount; // 折扣比例
  final String? notes;

  const BillItem({
    required this.id,
    required this.instrumentName,
    required this.reservationId,
    required this.usageDate,
    required this.duration,
    required this.unitPrice,
    required this.quantity,
    required this.amount,
    required this.discount,
    this.notes,
  });

  factory BillItem.fromJson(Map<String, dynamic> json) {
    return BillItem(
      id: json['id'] as String,
      instrumentName: json['instrument_name'] as String,
      reservationId: json['reservation_id'] as String,
      usageDate: DateTime.parse(json['usage_date'] as String),
      duration: (json['duration'] as num).toDouble(),
      unitPrice: (json['unit_price'] as num).toDouble(),
      quantity: (json['quantity'] as num).toDouble(),
      amount: (json['amount'] as num).toDouble(),
      discount: (json['discount'] as num).toDouble(),
      notes: json['notes'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'instrument_name': instrumentName,
      'reservation_id': reservationId,
      'usage_date': usageDate.toIso8601String(),
      'duration': duration,
      'unit_price': unitPrice,
      'quantity': quantity,
      'amount': amount,
      'discount': discount,
      'notes': notes,
    };
  }
}

/// 账单状态枚举
enum BillStatus {
  unpaid, // 未支付
  paid, // 已支付
  overdue, // 已逾期
  cancelled, // 已取消
}

/// 财务统计模型
class FinancialStats {
  final double totalRevenue;
  final double monthlyRevenue;
  final double pendingPayments;
  final int totalTransactions;
  final Map<String, double> revenueByInstrument; // 按仪器分类的收入
  final Map<String, double> revenueByUserType; // 按用户类型分类的收入
  final List<MonthlyRevenue> monthlyTrend; // 月度收入趋势

  const FinancialStats({
    required this.totalRevenue,
    required this.monthlyRevenue,
    required this.pendingPayments,
    required this.totalTransactions,
    required this.revenueByInstrument,
    required this.revenueByUserType,
    required this.monthlyTrend,
  });

  factory FinancialStats.fromJson(Map<String, dynamic> json) {
    return FinancialStats(
      totalRevenue: (json['total_revenue'] as num).toDouble(),
      monthlyRevenue: (json['monthly_revenue'] as num).toDouble(),
      pendingPayments: (json['pending_payments'] as num).toDouble(),
      totalTransactions: json['total_transactions'] as int,
      revenueByInstrument:
          Map<String, double>.from(json['revenue_by_instrument'] as Map),
      revenueByUserType:
          Map<String, double>.from(json['revenue_by_user_type'] as Map),
      monthlyTrend: (json['monthly_trend'] as List<dynamic>)
          .map((item) => MonthlyRevenue.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_revenue': totalRevenue,
      'monthly_revenue': monthlyRevenue,
      'pending_payments': pendingPayments,
      'total_transactions': totalTransactions,
      'revenue_by_instrument': revenueByInstrument,
      'revenue_by_user_type': revenueByUserType,
      'monthly_trend': monthlyTrend.map((item) => item.toJson()).toList(),
    };
  }
}

/// 月度收入数据
class MonthlyRevenue {
  final String month; // YYYY-MM格式
  final double revenue;
  final int transactionCount;

  const MonthlyRevenue({
    required this.month,
    required this.revenue,
    required this.transactionCount,
  });

  factory MonthlyRevenue.fromJson(Map<String, dynamic> json) {
    return MonthlyRevenue(
      month: json['month'] as String,
      revenue: (json['revenue'] as num).toDouble(),
      transactionCount: json['transaction_count'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'month': month,
      'revenue': revenue,
      'transaction_count': transactionCount,
    };
  }
}

/// 充值记录模型
class DepositRecord {
  final String id;
  final String researchGroupId;
  final String operatorId; // 操作员ID
  final double amount;
  final String paymentMethod; // 支付方式
  final String? transactionId; // 第三方交易ID
  final DepositStatus status;
  final String? notes;
  final DateTime depositDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  const DepositRecord({
    required this.id,
    required this.researchGroupId,
    required this.operatorId,
    required this.amount,
    required this.paymentMethod,
    this.transactionId,
    required this.status,
    this.notes,
    required this.depositDate,
    required this.createdAt,
    required this.updatedAt,
  });

  factory DepositRecord.fromJson(Map<String, dynamic> json) {
    return DepositRecord(
      id: json['id'] as String,
      researchGroupId: json['research_group_id'] as String,
      operatorId: json['operator_id'] as String,
      amount: (json['amount'] as num).toDouble(),
      paymentMethod: json['payment_method'] as String,
      transactionId: json['transaction_id'] as String?,
      status: DepositStatus.values.firstWhere(
        (status) => status.toString().split('.').last == json['status'],
        orElse: () => DepositStatus.pending,
      ),
      notes: json['notes'] as String?,
      depositDate: DateTime.parse(json['deposit_date'] as String),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'research_group_id': researchGroupId,
      'operator_id': operatorId,
      'amount': amount,
      'payment_method': paymentMethod,
      'transaction_id': transactionId,
      'status': status.toString().split('.').last,
      'notes': notes,
      'deposit_date': depositDate.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}

/// 充值状态枚举
enum DepositStatus {
  pending, // 待确认
  completed, // 已完成
  failed, // 失败
  cancelled, // 已取消
}
