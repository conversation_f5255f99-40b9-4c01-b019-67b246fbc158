package logger

import (
	"os"
	"path/filepath"
	"time"

	"instrument-management/internal/config"

	"github.com/sirupsen/logrus"
)

var logger *logrus.Logger

// Init 初始化日志
func Init() error {
	logger = logrus.New()

	// 设置日志级别
	level := config.GetString("log.level")
	switch level {
	case "debug":
		logger.SetLevel(logrus.DebugLevel)
	case "info":
		logger.SetLevel(logrus.InfoLevel)
	case "warn":
		logger.SetLevel(logrus.WarnLevel)
	case "error":
		logger.SetLevel(logrus.ErrorLevel)
	default:
		logger.SetLevel(logrus.InfoLevel)
	}

	// 设置日志格式
	format := config.GetString("log.format")
	if format == "json" {
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: time.RFC3339,
		})
	} else {
		logger.SetFormatter(&logrus.TextFormatter{
			FullTimestamp:   true,
			TimestampFormat: "2006-01-02 15:04:05",
		})
	}

	// 设置输出
	output := config.GetString("log.output")
	if output == "file" {
		logFile := config.GetString("log.file_path")
		if logFile == "" {
			logFile = "logs/app.log"
		}

		// 创建日志目录
		dir := filepath.Dir(logFile)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return err
		}

		file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			return err
		}
		logger.SetOutput(file)
	} else {
		logger.SetOutput(os.Stdout)
	}

	return nil
}

// GetLogger 获取日志实例
func GetLogger() *logrus.Logger {
	return logger
}

// Debug 记录调试日志
func Debug(args ...interface{}) {
	if logger != nil {
		logger.Debug(args...)
	}
}

// Info 记录信息日志
func Info(args ...interface{}) {
	if logger != nil {
		logger.Info(args...)
	}
}

// Warn 记录警告日志
func Warn(args ...interface{}) {
	if logger != nil {
		logger.Warn(args...)
	}
}

// Error 记录错误日志
func Error(args ...interface{}) {
	if logger != nil {
		logger.Error(args...)
	}
}

// Fatal 记录致命错误日志并退出
func Fatal(args ...interface{}) {
	if logger != nil {
		logger.Fatal(args...)
	}
}

// WithField 添加字段到日志
func WithField(key string, value interface{}) *logrus.Entry {
	if logger != nil {
		return logger.WithField(key, value)
	}
	return nil
}

// WithFields 添加多个字段到日志
func WithFields(fields logrus.Fields) *logrus.Entry {
	if logger != nil {
		return logger.WithFields(fields)
	}
	return nil
}

// WithError 添加错误到日志
func WithError(err error) *logrus.Entry {
	if logger != nil {
		return logger.WithError(err)
	}
	return nil
}













