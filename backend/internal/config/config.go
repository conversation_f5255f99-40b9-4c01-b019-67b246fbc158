package config

import (
	"fmt"
	"os"

	"github.com/joho/godotenv"
	"github.com/spf13/viper"
)

// Init 初始化配置
func Init() error {
	// 加载环境变量
	if err := loadEnv(); err != nil {
		return fmt.Errorf("加载环境变量失败: %w", err)
	}

	// 设置配置文件路径
	configPath := getConfigPath()

	// 设置viper配置
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(configPath)
	viper.AddConfigPath(".") // 当前目录

	// 设置默认值
	setDefaults()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 绑定环境变量
	bindEnvVars()

	// 验证配置
	if err := validateConfig(); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}

	return nil
}

// loadEnv 加载环境变量文件
func loadEnv() error {
	envPaths := []string{
		".env",
		"../.env",
		"../../.env",
	}

	for _, path := range envPaths {
		if _, err := os.Stat(path); err == nil {
			return godotenv.Load(path)
		}
	}

	return nil // 没有找到.env文件也不算错误
}

// getConfigPath 获取配置文件路径
func getConfigPath() string {
	// 首先检查环境变量
	if configPath := os.Getenv("CONFIG_PATH"); configPath != "" {
		return configPath
	}

	// 检查当前目录
	if _, err := os.Stat("config.yaml"); err == nil {
		return "."
	}

	// 检查上级目录
	if _, err := os.Stat("../config.yaml"); err == nil {
		return ".."
	}

	return "."
}

// setDefaults 设置默认值
func setDefaults() {
	// 应用默认值
	viper.SetDefault("app.name", "仪器共享管理系统")
	viper.SetDefault("app.version", "1.0.0")
	viper.SetDefault("app.env", "development")

	// 服务器默认值
	viper.SetDefault("server.host", "localhost")
	viper.SetDefault("server.port", "8080")
	viper.SetDefault("server.read_timeout", 30)
	viper.SetDefault("server.write_timeout", 30)

	// 数据库默认值
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 5432)
	viper.SetDefault("database.ssl_mode", "disable")
	viper.SetDefault("database.max_open_conns", 100)
	viper.SetDefault("database.max_idle_conns", 10)
	viper.SetDefault("database.conn_max_lifetime", 3600)

	// JWT默认值
	viper.SetDefault("jwt.expire_hours", 24)
	viper.SetDefault("jwt.refresh_expire_hours", 168)

	// 日志默认值
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.format", "json")
	viper.SetDefault("log.output", "stdout")

	// 上传默认值
	viper.SetDefault("upload.max_size", 10485760) // 10MB
	viper.SetDefault("upload.allowed_types", []string{".jpg", ".jpeg", ".png", ".pdf"})
	viper.SetDefault("upload.base_path", "uploads")

	// 缓存默认值
	viper.SetDefault("cache.expire_time", 3600)

	// 业务默认值
	viper.SetDefault("business.reservation.max_advance_days", 30)
	viper.SetDefault("business.reservation.min_duration_minutes", 30)
	viper.SetDefault("business.reservation.max_duration_hours", 8)
	viper.SetDefault("business.billing.currency", "CNY")
}

// bindEnvVars 绑定环境变量
func bindEnvVars() {
	// 数据库环境变量
	viper.BindEnv("database.host", "DB_HOST")
	viper.BindEnv("database.port", "DB_PORT")
	viper.BindEnv("database.username", "DB_USERNAME")
	viper.BindEnv("database.password", "DB_PASSWORD")
	viper.BindEnv("database.database", "DB_DATABASE")

	// JWT环境变量
	viper.BindEnv("jwt.secret", "JWT_SECRET")

	// 服务器环境变量
	viper.BindEnv("server.port", "PORT")

	// 第三方服务环境变量
	viper.BindEnv("third_party.sso.client_id", "SSO_CLIENT_ID")
	viper.BindEnv("third_party.sso.client_secret", "SSO_CLIENT_SECRET")

	viper.BindEnv("third_party.payment.alipay_app_id", "ALIPAY_APP_ID")
	viper.BindEnv("third_party.payment.alipay_private_key", "ALIPAY_PRIVATE_KEY")
}

// validateConfig 验证配置
func validateConfig() error {
	// 检查必需的配置项
	requiredKeys := []string{
		"jwt.secret",
		"database.username",
		"database.password",
		"database.database",
	}

	for _, key := range requiredKeys {
		if value := viper.GetString(key); value == "" {
			return fmt.Errorf("必需配置项缺失: %s", key)
		}
	}

	// 验证数据库端口
	if port := viper.GetInt("database.port"); port <= 0 || port > 65535 {
		return fmt.Errorf("无效的数据库端口: %d", port)
	}

	// 验证服务器端口
	if port := viper.GetString("server.port"); port == "" {
		return fmt.Errorf("服务器端口不能为空")
	}

	return nil
}

// GetString 获取字符串配置
func GetString(key string) string {
	return viper.GetString(key)
}

// GetInt 获取整数配置
func GetInt(key string) int {
	return viper.GetInt(key)
}

// GetBool 获取布尔配置
func GetBool(key string) bool {
	return viper.GetBool(key)
}

// GetStringSlice 获取字符串数组配置
func GetStringSlice(key string) []string {
	return viper.GetStringSlice(key)
}

// GetDuration 获取时间配置
func GetDuration(key string) int {
	return viper.GetInt(key)
}


