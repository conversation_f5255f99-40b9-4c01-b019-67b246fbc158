package services

import (
	"errors"
	"fmt"
	"time"

	"instrument-management/internal/models"
	"instrument-management/pkg/jwt"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// AuthService 认证服务
type AuthService struct {
	db *gorm.DB
}

// NewAuthService 创建认证服务实例
func NewAuthService(db *gorm.DB) *AuthService {
	return &AuthService{db: db}
}

// SSOAuthenticate SSO统一身份认证
func (s *AuthService) SSOAuthenticate(username, password string) (*models.User, error) {
	// 这里应该调用学校的统一身份认证API
	// 为了演示，我们使用模拟的认证逻辑

	// 在实际项目中，这里应该调用学校的SSO API
	// 例如：调用西北工业大学的统一身份认证服务

	// 查找用户
	var user models.User
	result := s.db.Where("username = ?", username).First(&user)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, result.Error
	}

	// 检查用户状态
	if user.Status != models.UserStatusActive {
		switch user.Status {
		case models.UserStatusPending:
			return nil, errors.New("用户待激活，请联系导师")
		case models.UserStatusInactive:
			return nil, errors.New("用户已被停用")
		case models.UserStatusSuspended:
			return nil, errors.New("用户已被暂停")
		default:
			return nil, errors.New("用户状态异常")
		}
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(password)); err != nil {
		return nil, errors.New("密码错误")
	}

	// 更新最后登录时间
	now := time.Now()
	user.LastLoginAt = &now
	if err := s.db.Save(&user).Error; err != nil {
		// 记录错误但不影响登录
		fmt.Printf("更新用户登录时间失败: %v\n", err)
	}

	return &user, nil
}

// RegisterStudent 学生注册
func (s *AuthService) RegisterStudent(req *RegisterStudentRequest) (*models.User, error) {
	// 检查用户名是否已存在
	var existingUser models.User
	if err := s.db.Where("username = ?", req.Username).First(&existingUser).Error; err == nil {
		return nil, errors.New("用户名已存在")
	}

	// 检查邮箱是否已存在
	if err := s.db.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		return nil, errors.New("邮箱已被注册")
	}

	// 验证课题组是否存在
	var researchGroup models.ResearchGroup
	if err := s.db.Where("id = ?", req.ResearchGroupID).First(&researchGroup).Error; err != nil {
		return nil, errors.New("课题组不存在")
	}

	// 创建用户
	user := &models.User{
		Username:         req.Username,
		Email:            req.Email,
		Phone:            req.Phone,
		Role:             models.UserRoleStudent,
		Status:           models.UserStatusPending,
		ResearchGroupID:  &req.ResearchGroupID,
		FullName:         req.FullName,
		Department:       req.Department,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	// 哈希密码（虽然学生注册后需要激活，但预设一个临时密码）
	tempPassword := "temp_" + req.Username // 临时密码，激活后需要修改
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(tempPassword), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("密码哈希失败: %w", err)
	}
	user.PasswordHash = string(hashedPassword)

	// 保存用户
	if err := s.db.Create(user).Error; err != nil {
		return nil, fmt.Errorf("创建用户失败: %w", err)
	}

	return user, nil
}

// RegisterTeacher 教师注册
func (s *AuthService) RegisterTeacher(req *RegisterTeacherRequest) (*models.User, error) {
	// 检查用户名是否已存在
	var existingUser models.User
	if err := s.db.Where("username = ?", req.Username).First(&existingUser).Error; err == nil {
		return nil, errors.New("用户名已存在")
	}

	// 检查邮箱是否已存在
	if err := s.db.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		return nil, errors.New("邮箱已被注册")
	}

	// 哈希密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("密码哈希失败: %w", err)
	}

	// 创建用户
	user := &models.User{
		Username:     req.Username,
		Email:        req.Email,
		Phone:        req.Phone,
		PasswordHash: string(hashedPassword),
		Role:         models.UserRoleTeacher,
		Status:       models.UserStatusActive,
		FullName:     &req.FullName,
		Department:   req.Department,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// 保存用户
	if err := s.db.Create(user).Error; err != nil {
		return nil, fmt.Errorf("创建用户失败: %w", err)
	}

	return user, nil
}

// ActivateStudent 激活学生账号
func (s *AuthService) ActivateStudent(studentID string, activatorID string) error {
	// 检查激活者是否有权限（必须是教师或管理员）
	var activator models.User
	if err := s.db.Where("id = ?", activatorID).First(&activator).Error; err != nil {
		return errors.New("激活者不存在")
	}

	if activator.Role != models.UserRoleTeacher && activator.Role != models.UserRoleAdmin {
		return errors.New("无权限激活学生账号")
	}

	// 查找学生
	var student models.User
	if err := s.db.Where("id = ?", studentID).First(&student).Error; err != nil {
		return errors.New("学生不存在")
	}

	// 检查学生是否已激活
	if student.Status == models.UserStatusActive {
		return errors.New("学生已激活")
	}

	// 检查学生角色
	if student.Role != models.UserRoleStudent {
		return errors.New("只能激活学生账号")
	}

	// 激活学生
	now := time.Now()
	student.Status = models.UserStatusActive
	student.ActivatedAt = &now
	student.UpdatedAt = now

	if err := s.db.Save(&student).Error; err != nil {
		return fmt.Errorf("激活学生失败: %w", err)
	}

	return nil
}

// GenerateToken 生成用户令牌
func (s *AuthService) GenerateToken(user *models.User) (string, error) {
	return jwt.GenerateToken(user)
}

// GenerateRefreshToken 生成刷新令牌
func (s *AuthService) GenerateRefreshToken(user *models.User) (string, error) {
	return jwt.GenerateRefreshToken(user)
}

// ValidateToken 验证令牌
func (s *AuthService) ValidateToken(tokenString string) (*jwt.Claims, error) {
	return jwt.ValidateToken(tokenString)
}

// RefreshToken 刷新令牌
func (s *AuthService) RefreshToken(refreshTokenString string) (string, error) {
	return jwt.RefreshToken(refreshTokenString)
}

// GetUserByID 根据ID获取用户
func (s *AuthService) GetUserByID(userID string) (*models.User, error) {
	var user models.User
	if err := s.db.Preload("ResearchGroup").Where("id = ?", userID).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

// UpdateUser 更新用户信息
func (s *AuthService) UpdateUser(userID string, updates map[string]interface{}) (*models.User, error) {
	var user models.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		return nil, err
	}

	// 更新字段
	if fullName, ok := updates["full_name"].(string); ok {
		user.FullName = &fullName
	}
	if department, ok := updates["department"].(string); ok {
		user.Department = &department
	}
	if avatarURL, ok := updates["avatar_url"].(string); ok {
		user.AvatarURL = &avatarURL
	}

	user.UpdatedAt = time.Now()

	if err := s.db.Save(&user).Error; err != nil {
		return nil, err
	}

	return &user, nil
}

// Logout 登出
func (s *AuthService) Logout(tokenString string) error {
	// 在实际项目中，可以将令牌加入黑名单
	// 这里暂时只记录日志
	fmt.Printf("用户登出，令牌: %s\n", tokenString)
	return nil
}

// RegisterStudentRequest 学生注册请求
type RegisterStudentRequest struct {
	Username        string `json:"username" binding:"required,min=3,max=50"`
	Email           string `json:"email" binding:"required,email"`
	Phone           string `json:"phone" binding:"required,len=11"`
	FullName        *string `json:"full_name,omitempty"`
	Department      *string `json:"department,omitempty"`
	ResearchGroupID string `json:"research_group_id" binding:"required"`
}

// RegisterTeacherRequest 教师注册请求
type RegisterTeacherRequest struct {
	Username   string  `json:"username" binding:"required,min=3,max=50"`
	Email      string  `json:"email" binding:"required,email"`
	Phone      string  `json:"phone" binding:"required,len=11"`
	Password   string  `json:"password" binding:"required,min=6,max=100"`
	FullName   string  `json:"full_name" binding:"required,min=2,max=50"`
	Department *string `json:"department,omitempty"`
}


