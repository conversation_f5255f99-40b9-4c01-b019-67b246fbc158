package services

import (
	"errors"
	"fmt"
	"time"

	"instrument-management/internal/models"

	"gorm.io/gorm"
)

// InstrumentService 仪器服务
type InstrumentService struct {
	db *gorm.DB
}

// NewInstrumentService 创建仪器服务实例
func NewInstrumentService(db *gorm.DB) *InstrumentService {
	return &InstrumentService{db: db}
}

// CreateInstrument 创建仪器
func (s *InstrumentService) CreateInstrument(req *CreateInstrumentRequest, creatorID string) (*models.Instrument, error) {
	// 检查创建者是否有权限
	var creator models.User
	if err := s.db.Where("id = ?", creatorID).First(&creator).Error; err != nil {
		return nil, errors.New("创建者不存在")
	}

	if creator.Role != models.UserRoleAdmin && creator.Role != models.UserRoleTechnician {
		return nil, errors.New("无权限创建仪器")
	}

	// 检查仪器负责人是否存在
	var responsibleUser models.User
	if err := s.db.Where("id = ?", req.ResponsibleUserID).First(&responsibleUser).Error; err != nil {
		return nil, errors.New("仪器负责人不存在")
	}

	if responsibleUser.Role != models.UserRoleTechnician && responsibleUser.Role != models.UserRoleAdmin {
		return nil, errors.New("仪器负责人必须是仪器负责人或管理员")
	}

	// 创建仪器
	instrument := &models.Instrument{
		Name:               req.Name,
		Description:        req.Description,
		Model:              req.Model,
		Manufacturer:       req.Manufacturer,
		Location:           req.Location,
		ImageURL:           req.ImageURL,
		Status:             models.InstrumentStatusOffline,
		ControlType:        models.InstrumentControlType(req.ControlType),
		RequiresTraining:   req.RequiresTraining,
		AcceptsSampleSubmission: req.AcceptsSampleSubmission,
		RequiresReservation:     req.RequiresReservation,
		ResponsibleUserID:       req.ResponsibleUserID,
		CreatedAt:               time.Now(),
		UpdatedAt:               time.Now(),
	}

	// 处理工作时间
	if req.WorkingHours != nil && len(req.WorkingHours) > 0 {
		var workingHours []models.WorkingHours
		for _, wh := range req.WorkingHours {
			workingHours = append(workingHours, models.WorkingHours{
				DayOfWeek:   wh.DayOfWeek,
				StartTime:   wh.StartTime,
				EndTime:     wh.EndTime,
				IsWorkingDay: wh.IsWorkingDay,
			})
		}
		instrument.WorkingHours = workingHours
	}

	// 处理计费配置
	if req.BillingConfig != nil {
		instrument.BillingConfig = &models.BillingConfig{
			BillingType: models.BillingType(req.BillingConfig.BillingType),
			BaseRate:    req.BillingConfig.BaseRate,
			Unit:        req.BillingConfig.Unit,
			TagDiscounts: fmt.Sprintf("%v", req.BillingConfig.TagDiscounts),
		}
	}

	// 处理用户标签
	if req.UserTags != nil && len(req.UserTags) > 0 {
		var userTags []models.UserTag
		for _, ut := range req.UserTags {
			userTags = append(userTags, models.UserTag{
				Name:     ut.Name,
				Description: ut.Description,
				Priority: ut.Priority,
				Discount: ut.Discount,
			})
		}
		instrument.UserTags = userTags
	}

	// 保存仪器
	if err := s.db.Create(instrument).Error; err != nil {
		return nil, fmt.Errorf("创建仪器失败: %w", err)
	}

	// 重新加载关联数据
	if err := s.db.Preload("ResponsibleUser").
		Preload("WorkingHours").
		Preload("BillingConfig").
		Preload("UserTags").
		Where("id = ?", instrument.ID).First(instrument).Error; err != nil {
		return nil, err
	}

	return instrument, nil
}

// UpdateInstrument 更新仪器
func (s *InstrumentService) UpdateInstrument(instrumentID string, req *UpdateInstrumentRequest, operatorID string) (*models.Instrument, error) {
	var instrument models.Instrument
	if err := s.db.Where("id = ?", instrumentID).First(&instrument).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("仪器不存在")
		}
		return nil, err
	}

	// 检查操作者是否有权限
	var operator models.User
	if err := s.db.Where("id = ?", operatorID).First(&operator).Error; err != nil {
		return nil, errors.New("操作者不存在")
	}

	if operator.Role != models.UserRoleAdmin && instrument.ResponsibleUserID != operatorID {
		return nil, errors.New("无权限修改仪器")
	}

	// 更新字段
	if req.Name != nil {
		instrument.Name = *req.Name
	}
	if req.Description != nil {
		instrument.Description = req.Description
	}
	if req.Model != nil {
		instrument.Model = *req.Model
	}
	if req.Manufacturer != nil {
		instrument.Manufacturer = *req.Manufacturer
	}
	if req.Location != nil {
		instrument.Location = *req.Location
	}
	if req.ImageURL != nil {
		instrument.ImageURL = req.ImageURL
	}
	if req.ControlType != nil {
		instrument.ControlType = models.InstrumentControlType(*req.ControlType)
	}
	if req.RequiresTraining != nil {
		instrument.RequiresTraining = *req.RequiresTraining
	}
	if req.AcceptsSampleSubmission != nil {
		instrument.AcceptsSampleSubmission = *req.AcceptsSampleSubmission
	}
	if req.RequiresReservation != nil {
		instrument.RequiresReservation = *req.RequiresReservation
	}
	if req.ResponsibleUserID != nil {
		// 检查新的负责人是否存在
		var newResponsible models.User
		if err := s.db.Where("id = ?", *req.ResponsibleUserID).First(&newResponsible).Error; err != nil {
			return nil, errors.New("新的仪器负责人不存在")
		}
		instrument.ResponsibleUserID = *req.ResponsibleUserID
	}

	instrument.UpdatedAt = time.Now()

	if err := s.db.Save(&instrument).Error; err != nil {
		return nil, fmt.Errorf("更新仪器失败: %w", err)
	}

	// 重新加载关联数据
	if err := s.db.Preload("ResponsibleUser").
		Preload("WorkingHours").
		Preload("BillingConfig").
		Preload("UserTags").
		Where("id = ?", instrument.ID).First(&instrument).Error; err != nil {
		return nil, err
	}

	return &instrument, nil
}

// GetInstrument 获取仪器详情
func (s *InstrumentService) GetInstrument(instrumentID string) (*models.Instrument, error) {
	var instrument models.Instrument
	if err := s.db.Preload("ResponsibleUser").
		Preload("WorkingHours").
		Preload("BillingConfig").
		Preload("UserTags").
		Where("id = ?", instrumentID).First(&instrument).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("仪器不存在")
		}
		return nil, err
	}
	return &instrument, nil
}

// ListInstruments 获取仪器列表
func (s *InstrumentService) ListInstruments(filter *InstrumentFilter, page, pageSize int) ([]*models.Instrument, int64, error) {
	var instruments []*models.Instrument
	var total int64

	query := s.db.Model(&models.Instrument{}).Preload("ResponsibleUser")

	// 应用过滤条件
	if filter != nil {
		if filter.Status != "" {
			query = query.Where("status = ?", filter.Status)
		}
		if filter.ResponsibleUserID != "" {
			query = query.Where("responsible_user_id = ?", filter.ResponsibleUserID)
		}
		if filter.Location != "" {
			query = query.Where("location ILIKE ?", "%"+filter.Location+"%")
		}
		if filter.Search != "" {
			searchTerm := "%" + filter.Search + "%"
			query = query.Where(
				"name ILIKE ? OR model ILIKE ? OR manufacturer ILIKE ?",
				searchTerm, searchTerm, searchTerm,
			)
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&instruments).Error; err != nil {
		return nil, 0, err
	}

	return instruments, total, nil
}

// UpdateInstrumentStatus 更新仪器状态
func (s *InstrumentService) UpdateInstrumentStatus(instrumentID string, status models.InstrumentStatus, operatorID string) error {
	var instrument models.Instrument
	if err := s.db.Where("id = ?", instrumentID).First(&instrument).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("仪器不存在")
		}
		return err
	}

	// 检查操作者是否有权限
	var operator models.User
	if err := s.db.Where("id = ?", operatorID).First(&operator).Error; err != nil {
		return errors.New("操作者不存在")
	}

	if operator.Role != models.UserRoleAdmin && instrument.ResponsibleUserID != operatorID {
		return errors.New("无权限修改仪器状态")
	}

	// 更新状态
	instrument.Status = status
	if status == models.InstrumentStatusScrapped {
		// 报废状态不可逆，需要额外确认
		return errors.New("报废状态不可逆，请联系管理员")
	}

	instrument.UpdatedAt = time.Now()

	if err := s.db.Save(&instrument).Error; err != nil {
		return fmt.Errorf("更新仪器状态失败: %w", err)
	}

	return nil
}

// DeleteInstrument 删除仪器
func (s *InstrumentService) DeleteInstrument(instrumentID string, operatorID string) error {
	var instrument models.Instrument
	if err := s.db.Where("id = ?", instrumentID).First(&instrument).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("仪器不存在")
		}
		return err
	}

	// 检查操作者是否有权限
	var operator models.User
	if err := s.db.Where("id = ?", operatorID).First(&operator).Error; err != nil {
		return errors.New("操作者不存在")
	}

	if operator.Role != models.UserRoleAdmin {
		return errors.New("只有管理员可以删除仪器")
	}

	// 检查是否有未完成的预约
	var reservationCount int64
	if err := s.db.Model(&models.Reservation{}).
		Where("instrument_id = ? AND status IN ?", instrumentID, []models.ReservationStatus{
			models.ReservationStatusPending,
			models.ReservationStatusApproved,
			models.ReservationStatusInProgress,
		}).Count(&reservationCount).Error; err != nil {
		return err
	}

	if reservationCount > 0 {
		return errors.New("仪器有未完成的预约，不能删除")
	}

	// 软删除：实际上我们应该使用GORM的软删除功能
	// 这里为了简化，直接物理删除
	if err := s.db.Delete(&instrument).Error; err != nil {
		return fmt.Errorf("删除仪器失败: %w", err)
	}

	return nil
}

// GetInstrumentStats 获取仪器统计信息
func (s *InstrumentService) GetInstrumentStats() (*InstrumentStats, error) {
	var stats InstrumentStats

	// 总仪器数
	if err := s.db.Model(&models.Instrument{}).Count(&stats.TotalInstruments).Error; err != nil {
		return nil, err
	}

	// 按状态统计
	var statusStats []struct {
		Status models.InstrumentStatus `json:"status"`
		Count  int64                   `json:"count"`
	}
	if err := s.db.Model(&models.Instrument{}).
		Select("status, COUNT(*) as count").
		Group("status").
		Scan(&statusStats).Error; err != nil {
		return nil, err
	}

	stats.InstrumentsByStatus = make(map[string]int64)
	for _, stat := range statusStats {
		stats.InstrumentsByStatus[string(stat.Status)] = stat.Count
	}

	// 按位置统计
	var locationStats []struct {
		Location string `json:"location"`
		Count    int64  `json:"count"`
	}
	if err := s.db.Model(&models.Instrument{}).
		Select("location, COUNT(*) as count").
		Group("location").
		Scan(&locationStats).Error; err != nil {
		return nil, err
	}

	stats.InstrumentsByLocation = make(map[string]int64)
	for _, stat := range locationStats {
		stats.InstrumentsByLocation[stat.Location] = stat.Count
	}

	return &stats, nil
}

// InstrumentFilter 仪器过滤器
type InstrumentFilter struct {
	Status            string `json:"status,omitempty"`
	ResponsibleUserID string `json:"responsible_user_id,omitempty"`
	Location          string `json:"location,omitempty"`
	Search            string `json:"search,omitempty"`
}

// CreateInstrumentRequest 创建仪器请求
type CreateInstrumentRequest struct {
	Name                     string                          `json:"name" binding:"required"`
	Description              *string                         `json:"description,omitempty"`
	Model                    string                          `json:"model" binding:"required"`
	Manufacturer             string                          `json:"manufacturer" binding:"required"`
	Location                 string                          `json:"location" binding:"required"`
	ImageURL                 *string                         `json:"image_url,omitempty"`
	ControlType              string                          `json:"control_type" binding:"required"`
	RequiresTraining         bool                            `json:"requires_training"`
	AcceptsSampleSubmission  bool                            `json:"accepts_sample_submission"`
	RequiresReservation      bool                            `json:"requires_reservation"`
	ResponsibleUserID        string                          `json:"responsible_user_id" binding:"required"`
	WorkingHours             []CreateWorkingHoursRequest     `json:"working_hours,omitempty"`
	BillingConfig            *CreateBillingConfigRequest     `json:"billing_config,omitempty"`
	UserTags                 []CreateUserTagRequest          `json:"user_tags,omitempty"`
}

// UpdateInstrumentRequest 更新仪器请求
type UpdateInstrumentRequest struct {
	Name                    *string                         `json:"name,omitempty"`
	Description             *string                         `json:"description,omitempty"`
	Model                   *string                         `json:"model,omitempty"`
	Manufacturer            *string                         `json:"manufacturer,omitempty"`
	Location                *string                         `json:"location,omitempty"`
	ImageURL                *string                         `json:"image_url,omitempty"`
	ControlType             *string                         `json:"control_type,omitempty"`
	RequiresTraining        *bool                           `json:"requires_training,omitempty"`
	AcceptsSampleSubmission *bool                           `json:"accepts_sample_submission,omitempty"`
	RequiresReservation     *bool                           `json:"requires_reservation,omitempty"`
	ResponsibleUserID       *string                         `json:"responsible_user_id,omitempty"`
}

// CreateWorkingHoursRequest 创建工作时间请求
type CreateWorkingHoursRequest struct {
	DayOfWeek   int    `json:"day_of_week" binding:"required,min=1,max=7"`
	StartTime   string `json:"start_time" binding:"required"`
	EndTime     string `json:"end_time" binding:"required"`
	IsWorkingDay bool  `json:"is_working_day"`
}

// CreateBillingConfigRequest 创建计费配置请求
type CreateBillingConfigRequest struct {
	BillingType  string             `json:"billing_type" binding:"required"`
	BaseRate     float64            `json:"base_rate" binding:"required"`
	Unit         string             `json:"unit" binding:"required"`
	TagDiscounts map[string]float64 `json:"tag_discounts,omitempty"`
}

// CreateUserTagRequest 创建用户标签请求
type CreateUserTagRequest struct {
	Name        string  `json:"name" binding:"required"`
	Description *string `json:"description,omitempty"`
	Priority    float64 `json:"priority"`
	Discount    float64 `json:"discount"`
}

// InstrumentStats 仪器统计信息
type InstrumentStats struct {
	TotalInstruments      int64             `json:"total_instruments"`
	InstrumentsByStatus   map[string]int64  `json:"instruments_by_status"`
	InstrumentsByLocation map[string]int64  `json:"instruments_by_location"`
}


