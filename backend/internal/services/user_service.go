package services

import (
	"errors"
	"fmt"
	"time"

	"instrument-management/internal/models"

	"gorm.io/gorm"
)

// UserService 用户服务
type UserService struct {
	db *gorm.DB
}

// NewUserService 创建用户服务实例
func NewUserService(db *gorm.DB) *UserService {
	return &UserService{db: db}
}

// GetUserByID 根据ID获取用户
func (s *UserService) GetUserByID(userID string) (*models.User, error) {
	var user models.User
	if err := s.db.Preload("ResearchGroup").Where("id = ?", userID).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, err
	}
	return &user, nil
}

// GetUserByUsername 根据用户名获取用户
func (s *UserService) GetUserByUsername(username string) (*models.User, error) {
	var user models.User
	if err := s.db.Preload("ResearchGroup").Where("username = ?", username).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, err
	}
	return &user, nil
}

// ListUsers 获取用户列表
func (s *UserService) ListUsers(filter *UserFilter, page, pageSize int) ([]*models.User, int64, error) {
	var users []*models.User
	var total int64

	query := s.db.Model(&models.User{}).Preload("ResearchGroup")

	// 应用过滤条件
	if filter != nil {
		if filter.Role != "" {
			query = query.Where("role = ?", filter.Role)
		}
		if filter.Status != "" {
			query = query.Where("status = ?", filter.Status)
		}
		if filter.Department != "" {
			query = query.Where("department = ?", filter.Department)
		}
		if filter.ResearchGroupID != "" {
			query = query.Where("research_group_id = ?", filter.ResearchGroupID)
		}
		if filter.Search != "" {
			searchTerm := "%" + filter.Search + "%"
			query = query.Where(
				"username ILIKE ? OR email ILIKE ? OR full_name ILIKE ?",
				searchTerm, searchTerm, searchTerm,
			)
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&users).Error; err != nil {
		return nil, 0, err
	}

	return users, total, nil
}

// UpdateUser 更新用户信息
func (s *UserService) UpdateUser(userID string, updates *UserUpdateRequest) (*models.User, error) {
	var user models.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, err
	}

	// 更新字段
	if updates.FullName != nil {
		user.FullName = updates.FullName
	}
	if updates.Department != nil {
		user.Department = updates.Department
	}
	if updates.AvatarURL != nil {
		user.AvatarURL = updates.AvatarURL
	}
	if updates.Phone != nil {
		user.Phone = *updates.Phone
	}
	if updates.Email != nil {
		user.Email = *updates.Email
	}

	user.UpdatedAt = time.Now()

	if err := s.db.Save(&user).Error; err != nil {
		return nil, fmt.Errorf("更新用户失败: %w", err)
	}

	// 重新加载关联数据
	if err := s.db.Preload("ResearchGroup").Where("id = ?", user.ID).First(&user).Error; err != nil {
		return nil, err
	}

	return &user, nil
}

// UpdateUserStatus 更新用户状态
func (s *UserService) UpdateUserStatus(userID string, status models.UserStatus, operatorID string) error {
	// 检查操作者是否有权限
	var operator models.User
	if err := s.db.Where("id = ?", operatorID).First(&operator).Error; err != nil {
		return errors.New("操作者不存在")
	}

	if operator.Role != models.UserRoleAdmin && operator.Role != models.UserRoleTeacher {
		return errors.New("无权限修改用户状态")
	}

	var user models.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不存在")
		}
		return err
	}

	// 更新状态
	user.Status = status
	user.UpdatedAt = time.Now()

	if err := s.db.Save(&user).Error; err != nil {
		return fmt.Errorf("更新用户状态失败: %w", err)
	}

	return nil
}

// DeleteUser 删除用户（软删除）
func (s *UserService) DeleteUser(userID string, operatorID string) error {
	// 检查操作者是否有权限
	var operator models.User
	if err := s.db.Where("id = ?", operatorID).First(&operator).Error; err != nil {
		return errors.New("操作者不存在")
	}

	if operator.Role != models.UserRoleAdmin {
		return errors.New("只有管理员可以删除用户")
	}

	var user models.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不存在")
		}
		return err
	}

	// 软删除：将状态设置为inactive
	user.Status = models.UserStatusInactive
	user.UpdatedAt = time.Now()

	if err := s.db.Save(&user).Error; err != nil {
		return fmt.Errorf("删除用户失败: %w", err)
	}

	return nil
}

// CreateResearchGroup 创建课题组
func (s *UserService) CreateResearchGroup(req *CreateResearchGroupRequest, creatorID string) (*models.ResearchGroup, error) {
	// 检查创建者是否有权限
	var creator models.User
	if err := s.db.Where("id = ?", creatorID).First(&creator).Error; err != nil {
		return nil, errors.New("创建者不存在")
	}

	if creator.Role != models.UserRoleAdmin && creator.Role != models.UserRoleTeacher {
		return nil, errors.New("无权限创建课题组")
	}

	// 检查名称是否重复
	var existingGroup models.ResearchGroup
	if err := s.db.Where("name = ?", req.Name).First(&existingGroup).Error; err == nil {
		return nil, errors.New("课题组名称已存在")
	}

	// 创建课题组
	group := &models.ResearchGroup{
		Name:        req.Name,
		Description: req.Description,
		LeaderID:    creatorID,
		Balance:     0,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := s.db.Create(group).Error; err != nil {
		return nil, fmt.Errorf("创建课题组失败: %w", err)
	}

	// 重新加载关联数据
	if err := s.db.Preload("Leader").Where("id = ?", group.ID).First(&group).Error; err != nil {
		return nil, err
	}

	return group, nil
}

// UpdateResearchGroup 更新课题组
func (s *UserService) UpdateResearchGroup(groupID string, req *UpdateResearchGroupRequest, operatorID string) (*models.ResearchGroup, error) {
	var group models.ResearchGroup
	if err := s.db.Where("id = ?", groupID).First(&group).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("课题组不存在")
		}
		return nil, err
	}

	// 检查操作者是否有权限
	var operator models.User
	if err := s.db.Where("id = ?", operatorID).First(&operator).Error; err != nil {
		return nil, errors.New("操作者不存在")
	}

	if operator.Role != models.UserRoleAdmin && group.LeaderID != operatorID {
		return nil, errors.New("无权限修改课题组")
	}

	// 更新字段
	if req.Name != nil {
		group.Name = *req.Name
	}
	if req.Description != nil {
		group.Description = req.Description
	}

	group.UpdatedAt = time.Now()

	if err := s.db.Save(&group).Error; err != nil {
		return nil, fmt.Errorf("更新课题组失败: %w", err)
	}

	// 重新加载关联数据
	if err := s.db.Preload("Leader").Preload("Members").Where("id = ?", group.ID).First(&group).Error; err != nil {
		return nil, err
	}

	return &group, nil
}

// ListResearchGroups 获取课题组列表
func (s *UserService) ListResearchGroups(page, pageSize int) ([]*models.ResearchGroup, int64, error) {
	var groups []*models.ResearchGroup
	var total int64

	query := s.db.Model(&models.ResearchGroup{}).Preload("Leader").Preload("Members")

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&groups).Error; err != nil {
		return nil, 0, err
	}

	return groups, total, nil
}

// AddUserToResearchGroup 将用户添加到课题组
func (s *UserService) AddUserToResearchGroup(userID, groupID string, operatorID string) error {
	// 检查操作者是否有权限
	var operator models.User
	if err := s.db.Where("id = ?", operatorID).First(&operator).Error; err != nil {
		return errors.New("操作者不存在")
	}

	var group models.ResearchGroup
	if err := s.db.Where("id = ?", groupID).First(&group).Error; err != nil {
		return errors.New("课题组不存在")
	}

	if operator.Role != models.UserRoleAdmin && group.LeaderID != operatorID {
		return errors.New("无权限管理课题组成员")
	}

	var user models.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		return errors.New("用户不存在")
	}

	// 更新用户课题组
	user.ResearchGroupID = &groupID
	user.UpdatedAt = time.Now()

	if err := s.db.Save(&user).Error; err != nil {
		return fmt.Errorf("添加用户到课题组失败: %w", err)
	}

	return nil
}

// RemoveUserFromResearchGroup 从课题组移除用户
func (s *UserService) RemoveUserFromResearchGroup(userID, groupID string, operatorID string) error {
	// 检查操作者是否有权限
	var operator models.User
	if err := s.db.Where("id = ?", operatorID).First(&operator).Error; err != nil {
		return errors.New("操作者不存在")
	}

	var group models.ResearchGroup
	if err := s.db.Where("id = ?", groupID).First(&group).Error; err != nil {
		return errors.New("课题组不存在")
	}

	if operator.Role != models.UserRoleAdmin && group.LeaderID != operatorID {
		return errors.New("无权限管理课题组成员")
	}

	var user models.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		return errors.New("用户不存在")
	}

	// 移除用户课题组
	user.ResearchGroupID = nil
	user.UpdatedAt = time.Now()

	if err := s.db.Save(&user).Error; err != nil {
		return fmt.Errorf("从课题组移除用户失败: %w", err)
	}

	return nil
}

// UserFilter 用户过滤器
type UserFilter struct {
	Role            string `json:"role,omitempty"`
	Status          string `json:"status,omitempty"`
	Department      string `json:"department,omitempty"`
	ResearchGroupID string `json:"research_group_id,omitempty"`
	Search          string `json:"search,omitempty"`
}

// UserUpdateRequest 用户更新请求
type UserUpdateRequest struct {
	FullName   *string `json:"full_name,omitempty"`
	Department *string `json:"department,omitempty"`
	AvatarURL  *string `json:"avatar_url,omitempty"`
	Phone      *string `json:"phone,omitempty"`
	Email      *string `json:"email,omitempty"`
}

// CreateResearchGroupRequest 创建课题组请求
type CreateResearchGroupRequest struct {
	Name        string  `json:"name" binding:"required,min=2,max=100"`
	Description *string `json:"description,omitempty"`
}

// UpdateResearchGroupRequest 更新课题组请求
type UpdateResearchGroupRequest struct {
	Name        *string `json:"name,omitempty"`
	Description *string `json:"description,omitempty"`
}


