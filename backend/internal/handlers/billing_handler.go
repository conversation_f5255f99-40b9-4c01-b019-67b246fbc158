package handlers

import (
	"net/http"
	"strconv"
	"time"

	"instrument-management/internal/models"
	"instrument-management/internal/services"

	"github.com/gin-gonic/gin"
)

// BillingHandler 计费处理器
type BillingHandler struct {
	billingService *services.BillingService
}

// NewBillingHandler 创建计费处理器
func NewBillingHandler(billingService *services.BillingService) *BillingHandler {
	return &BillingHandler{
		billingService: billingService,
	}
}

// CalculateCharge 计算费用
// @Summary 计算预约费用
// @Description 根据预约信息计算应收费用
// @Tags 计费
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "预约ID"
// @Success 200 {object} ChargeCalculationResponse
// @Router /billing/calculate/{id} [get]
func (h *BillingHandler) CalculateCharge(c *gin.Context) {
	reservationID := c.Param("id")
	if reservationID == "" {
		c.<PERSON>(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "预约ID不能为空",
		})
		return
	}

	calculation, err := h.billingService.CalculateReservationCharge(reservationID)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "费用计算失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, ChargeCalculationResponse{
		Success: true,
		Message: "计算成功",
		Data:    calculation,
	})
}

// CreateTransaction 创建交易记录
// @Summary 创建交易记录
// @Description 创建新的交易记录
// @Tags 计费
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body CreateTransactionRequest true "创建交易请求"
// @Success 201 {object} TransactionResponse
// @Failure 400 {object} ErrorResponse
// @Router /billing/transactions [post]
func (h *BillingHandler) CreateTransaction(c *gin.Context) {
	operatorID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	var req CreateTransactionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	transaction, err := h.billingService.CreateTransaction(&services.CreateTransactionRequest{
		UserID:               req.UserID,
		Type:                 req.Type,
		Amount:               req.Amount,
		Description:          req.Description,
		RelatedReservationID: req.RelatedReservationID,
		RelatedInstrumentID:  req.RelatedInstrumentID,
	}, operatorID.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "创建交易记录失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, TransactionResponse{
		Success: true,
		Message: "创建成功",
		Data:    transaction,
	})
}

// CreateDeposit 创建充值记录
// @Summary 创建充值记录
// @Description 为课题组创建充值记录
// @Tags 计费
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body CreateDepositRequest true "创建充值请求"
// @Success 201 {object} DepositResponse
// @Failure 400 {object} ErrorResponse
// @Router /billing/deposit [post]
func (h *BillingHandler) CreateDeposit(c *gin.Context) {
	operatorID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	var req CreateDepositRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	deposit, err := h.billingService.CreateDeposit(&services.CreateDepositRequest{
		ResearchGroupID: req.ResearchGroupID,
		Amount:          req.Amount,
		PaymentMethod:   req.PaymentMethod,
		TransactionID:   req.TransactionID,
		Notes:           req.Notes,
	}, operatorID.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "创建充值记录失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, DepositResponse{
		Success: true,
		Message: "充值成功",
		Data:    deposit,
	})
}

// CreateBill 创建账单
// @Summary 创建账单
// @Description 为课题组创建账单
// @Tags 计费
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body CreateBillRequest true "创建账单请求"
// @Success 201 {object} BillResponse
// @Failure 400 {object} ErrorResponse
// @Router /billing/bills [post]
func (h *BillingHandler) CreateBill(c *gin.Context) {
	operatorID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	var req CreateBillRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	bill, err := h.billingService.CreateBill(req.ResearchGroupID, req.Title, req.Description, operatorID.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "创建账单失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, BillResponse{
		Success: true,
		Message: "创建成功",
		Data:    bill,
	})
}

// PayBill 支付账单
// @Summary 支付账单
// @Description 支付指定的账单
// @Tags 计费
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path string true "账单ID"
// @Success 200 {object} SuccessResponse
// @Failure 403 {object} ErrorResponse
// @Router /billing/bills/{id}/pay [post]
func (h *BillingHandler) PayBill(c *gin.Context) {
	billID := c.Param("id")
	if billID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "账单ID不能为空",
		})
		return
	}

	operatorID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Success: false,
			Message: "用户未认证",
		})
		return
	}

	err := h.billingService.PayBill(billID, operatorID.(string))
	if err != nil {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Success: false,
			Message: "支付账单失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "支付成功",
	})
}

// ListTransactions 获取交易记录列表
// @Summary 获取交易记录列表
// @Description 获取交易记录列表，支持过滤和分页
// @Tags 计费
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param research_group_id query string false "课题组ID"
// @Param type query string false "交易类型"
// @Param status query string false "交易状态"
// @Param start_date query string false "开始日期"
// @Param end_date query string false "结束日期"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} TransactionListResponse
// @Router /billing/transactions [get]
func (h *BillingHandler) ListTransactions(c *gin.Context) {
	// 构建过滤器
	filter := &services.TransactionFilter{
		ResearchGroupID: c.Query("research_group_id"),
		Type:            c.Query("type"),
		Status:          c.Query("status"),
	}

	// 解析日期参数
	if startDateStr := c.Query("start_date"); startDateStr != "" {
		if startDate, err := time.Parse("2006-01-02", startDateStr); err == nil {
			filter.StartDate = &startDate
		}
	}
	if endDateStr := c.Query("end_date"); endDateStr != "" {
		if endDate, err := time.Parse("2006-01-02", endDateStr); err == nil {
			filter.EndDate = &endDate
		}
	}

	// 分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	transactions, total, err := h.billingService.ListTransactions(filter, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Success: false,
			Message: "获取交易记录失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, TransactionListResponse{
		Success: true,
		Message: "获取成功",
		Data: TransactionListData{
			Transactions: transactions,
			Pagination: Pagination{
				Page:     page,
				PageSize: pageSize,
				Total:    int(total),
			},
		},
	})
}

// ListBills 获取账单列表
// @Summary 获取账单列表
// @Description 获取账单列表，支持过滤和分页
// @Tags 计费
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param research_group_id query string false "课题组ID"
// @Param status query string false "账单状态"
// @Param start_date query string false "开始日期"
// @Param end_date query string false "结束日期"
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(10)
// @Success 200 {object} BillListResponse
// @Router /billing/bills [get]
func (h *BillingHandler) ListBills(c *gin.Context) {
	// 构建过滤器
	filter := &services.BillFilter{
		ResearchGroupID: c.Query("research_group_id"),
		Status:          c.Query("status"),
	}

	// 解析日期参数
	if startDateStr := c.Query("start_date"); startDateStr != "" {
		if startDate, err := time.Parse("2006-01-02", startDateStr); err == nil {
			filter.StartDate = &startDate
		}
	}
	if endDateStr := c.Query("end_date"); endDateStr != "" {
		if endDate, err := time.Parse("2006-01-02", endDateStr); err == nil {
			filter.EndDate = &endDate
		}
	}

	// 分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	bills, total, err := h.billingService.ListBills(filter, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Success: false,
			Message: "获取账单列表失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, BillListResponse{
		Success: true,
		Message: "获取成功",
		Data: BillListData{
			Bills: bills,
			Pagination: Pagination{
				Page:     page,
				PageSize: pageSize,
				Total:    int(total),
			},
		},
	})
}

// GetFinancialStats 获取财务统计
// @Summary 获取财务统计
// @Description 获取财务统计信息
// @Tags 计费
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param research_group_id query string false "课题组ID"
// @Success 200 {object} FinancialStatsResponse
// @Router /billing/stats [get]
func (h *BillingHandler) GetFinancialStats(c *gin.Context) {
	var researchGroupID *string
	if rgID := c.Query("research_group_id"); rgID != "" {
		researchGroupID = &rgID
	}

	stats, err := h.billingService.GetFinancialStats(researchGroupID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Success: false,
			Message: "获取财务统计失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, FinancialStatsResponse{
		Success: true,
		Message: "获取成功",
		Data:    stats,
	})
}

// CreateTransactionRequest 创建交易请求
type CreateTransactionRequest struct {
	UserID               string  `json:"user_id" binding:"required"`
	Type                 string  `json:"type" binding:"required"`
	Amount               float64 `json:"amount" binding:"required"`
	Description          string  `json:"description" binding:"required"`
	RelatedReservationID *string `json:"related_reservation_id,omitempty"`
	RelatedInstrumentID  *string `json:"related_instrument_id,omitempty"`
}

// CreateDepositRequest 创建充值请求
type CreateDepositRequest struct {
	ResearchGroupID string  `json:"research_group_id" binding:"required"`
	Amount          float64 `json:"amount" binding:"required"`
	PaymentMethod   string  `json:"payment_method" binding:"required"`
	TransactionID   *string `json:"transaction_id,omitempty"`
	Notes           *string `json:"notes,omitempty"`
}

// CreateBillRequest 创建账单请求
type CreateBillRequest struct {
	ResearchGroupID string `json:"research_group_id" binding:"required"`
	Title           string `json:"title" binding:"required"`
	Description     string `json:"description" binding:"required"`
}

// ChargeCalculationResponse 费用计算响应
type ChargeCalculationResponse struct {
	Success bool                          `json:"success"`
	Message string                        `json:"message"`
	Data    *services.ChargeCalculation   `json:"data"`
}

// TransactionResponse 交易响应
type TransactionResponse struct {
	Success bool                  `json:"success"`
	Message string                `json:"message"`
	Data    *models.Transaction   `json:"data"`
}

// DepositResponse 充值响应
type DepositResponse struct {
	Success bool                     `json:"success"`
	Message string                   `json:"message"`
	Data    *models.DepositRecord    `json:"data"`
}

// BillResponse 账单响应
type BillResponse struct {
	Success bool          `json:"success"`
	Message string        `json:"message"`
	Data    *models.Bill  `json:"data"`
}

// TransactionListResponse 交易列表响应
type TransactionListResponse struct {
	Success bool                    `json:"success"`
	Message string                  `json:"message"`
	Data    TransactionListData     `json:"data"`
}

// TransactionListData 交易列表数据
type TransactionListData struct {
	Transactions []*models.Transaction `json:"transactions"`
	Pagination   Pagination            `json:"pagination"`
}

// BillListResponse 账单列表响应
type BillListResponse struct {
	Success bool           `json:"success"`
	Message string         `json:"message"`
	Data    BillListData   `json:"data"`
}

// BillListData 账单列表数据
type BillListData struct {
	Bills      []*models.Bill `json:"bills"`
	Pagination Pagination      `json:"pagination"`
}

// FinancialStatsResponse 财务统计响应
type FinancialStatsResponse struct {
	Success bool                     `json:"success"`
	Message string                   `json:"message"`
	Data    *models.FinancialStats   `json:"data"`
}
