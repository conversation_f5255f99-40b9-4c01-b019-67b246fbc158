package handlers

import (
	"net/http"

	"instrument-management/internal/models"
	"instrument-management/internal/services"

	"github.com/gin-gonic/gin"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	authService *services.AuthService
	userService *services.UserService
}

// NewAuthHandler 创建认证处理器
func NewAuthHandler(authService *services.AuthService, userService *services.UserService) *AuthHandler {
	return &AuthHandler{
		authService: authService,
		userService: userService,
	}
}

// SSOLogin SSO登录
// @Summary SSO统一身份认证登录
// @Description 使用西北工业大学统一身份认证登录
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body SSOLoginRequest true "登录请求"
// @Success 200 {object} AuthResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Router /auth/sso/login [post]
func (h *AuthHandler) SSOLogin(c *gin.Context) {
	var req SSOLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	// SSO认证
	user, err := h.authService.SSOAuthenticate(req.Username, req.Password)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Success: false,
			Message: "认证失败",
			Error:   err.Error(),
		})
		return
	}

	// 生成令牌
	token, err := h.authService.GenerateToken(user)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Success: false,
			Message: "生成令牌失败",
			Error:   err.Error(),
		})
		return
	}

	refreshToken, err := h.authService.GenerateRefreshToken(user)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Success: false,
			Message: "生成刷新令牌失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, AuthResponse{
		Success: true,
		Message: "登录成功",
		Data: AuthData{
			Token:        token,
			RefreshToken: refreshToken,
			User:         *user,
		},
	})
}

// RegisterStudent 学生注册
// @Summary 学生注册
// @Description 学生注册账号，需要导师激活
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body RegisterStudentRequest true "学生注册请求"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Router /auth/register/student [post]
func (h *AuthHandler) RegisterStudent(c *gin.Context) {
	var req services.RegisterStudentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	user, err := h.authService.RegisterStudent(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "注册失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "注册成功，请等待导师激活",
		Data:    user,
	})
}

// RegisterTeacher 教师注册
// @Summary 教师注册
// @Description 教师注册账号，注册后直接激活
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body RegisterTeacherRequest true "教师注册请求"
// @Success 200 {object} AuthResponse
// @Failure 400 {object} ErrorResponse
// @Router /auth/register/teacher [post]
func (h *AuthHandler) RegisterTeacher(c *gin.Context) {
	var req services.RegisterTeacherRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	user, err := h.authService.RegisterTeacher(&req)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "注册失败",
			Error:   err.Error(),
		})
		return
	}

	// 生成令牌
	token, err := h.authService.GenerateToken(user)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Success: false,
			Message: "生成令牌失败",
			Error:   err.Error(),
		})
		return
	}

	refreshToken, err := h.authService.GenerateRefreshToken(user)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Success: false,
			Message: "生成刷新令牌失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, AuthResponse{
		Success: true,
		Message: "注册成功",
		Data: AuthData{
			Token:        token,
			RefreshToken: refreshToken,
			User:         *user,
		},
	})
}

// RefreshToken 刷新令牌
// @Summary 刷新访问令牌
// @Description 使用刷新令牌获取新的访问令牌
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body RefreshTokenRequest true "刷新令牌请求"
// @Success 200 {object} RefreshTokenResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Router /auth/refresh [post]
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "请求参数错误",
			Error:   err.Error(),
		})
		return
	}

	token, err := h.authService.RefreshToken(req.RefreshToken)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Success: false,
			Message: "刷新令牌失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, RefreshTokenResponse{
		Success: true,
		Message: "刷新成功",
		Data: RefreshTokenData{
			Token: token,
		},
	})
}

// Logout 登出
// @Summary 用户登出
// @Description 使当前访问令牌失效
// @Tags 认证
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} SuccessResponse
// @Router /auth/logout [post]
func (h *AuthHandler) Logout(c *gin.Context) {
	// 从请求头获取令牌
	token := c.GetHeader("Authorization")
	if token == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Success: false,
			Message: "缺少认证令牌",
		})
		return
	}

	// 如果令牌以"Bearer "开头，去掉前缀
	if len(token) > 7 && token[:7] == "Bearer " {
		token = token[7:]
	}

	err := h.authService.Logout(token)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Success: false,
			Message: "登出失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Success: true,
		Message: "登出成功",
	})
}

// SSOLoginRequest SSO登录请求
type SSOLoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// RegisterTeacherRequest 教师注册请求
type RegisterTeacherRequest struct {
	Username   string  `json:"username" binding:"required,min=3,max=50"`
	Email      string  `json:"email" binding:"required,email"`
	Phone      string  `json:"phone" binding:"required,len=11"`
	Password   string  `json:"password" binding:"required,min=6,max=100"`
	FullName   string  `json:"full_name" binding:"required,min=2,max=50"`
	Department *string `json:"department,omitempty"`
}

// RefreshTokenRequest 刷新令牌请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// AuthResponse 认证响应
type AuthResponse struct {
	Success bool      `json:"success"`
	Message string    `json:"message"`
	Data    AuthData  `json:"data"`
}

// AuthData 认证数据
type AuthData struct {
	Token        string      `json:"token"`
	RefreshToken string      `json:"refresh_token"`
	User         models.User `json:"user"`
}

// RefreshTokenResponse 刷新令牌响应
type RefreshTokenResponse struct {
	Success bool               `json:"success"`
	Message string             `json:"message"`
	Data    RefreshTokenData   `json:"data"`
}

// RefreshTokenData 刷新令牌数据
type RefreshTokenData struct {
	Token string `json:"token"`
}

// SuccessResponse 成功响应
type SuccessResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Error   string `json:"error,omitempty"`
}










