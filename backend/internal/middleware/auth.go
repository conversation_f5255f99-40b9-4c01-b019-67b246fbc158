package middleware

import (
	"fmt"
	"net/http"
	"strings"

	"instrument-management/pkg/jwt"

	"github.com/gin-gonic/gin"
)

// JWTAuth JWT认证中间件
func JWTAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			c.J<PERSON>(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "缺少认证令牌",
			})
			c.Abort()
			return
		}

		// 提取令牌
		var token string
		if strings.HasPrefix(authHeader, "Bearer ") {
			token = strings.TrimPrefix(authHeader, "Bearer ")
		} else {
			token = authHeader
		}

		if token == "" {
			c.J<PERSON>(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "无效的认证令牌格式",
			})
			c.Abort()
			return
		}

		// 验证令牌
		claims, err := jwt.ValidateToken(token)
		if err != nil {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "令牌验证失败",
				"error":   err.<PERSON>(),
			})
			c.Abort()
			return
		}

		// 将用户信息存储在上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("role", claims.Role)

		c.Next()
	}
}

// RoleAuth 角色权限中间件
func RoleAuth(requiredRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		role, exists := c.Get("role")
		if !exists {
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"message": "无法获取用户角色",
			})
			c.Abort()
			return
		}

		userRole := role.(string)
		hasPermission := false

		for _, requiredRole := range requiredRoles {
			if userRole == requiredRole {
				hasPermission = true
				break
			}
		}

		if !hasPermission {
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"message": "权限不足",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// AdminAuth 管理员权限中间件
func AdminAuth() gin.HandlerFunc {
	return RoleAuth("admin")
}

// TechnicianAuth 仪器负责人权限中间件
func TechnicianAuth() gin.HandlerFunc {
	return RoleAuth("admin", "technician")
}

// TeacherAuth 教师权限中间件
func TeacherAuth() gin.HandlerFunc {
	return RoleAuth("admin", "teacher")
}

// CORS 跨域中间件
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Header("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// RequestID 请求ID中间件
func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 生成请求ID（在实际项目中可以使用UUID或雪花算法）
		requestID := "req_" + c.ClientIP() + "_" + c.Request.Method + "_" + c.Request.URL.Path

		c.Set("request_id", requestID)
		c.Header("X-Request-ID", requestID)

		c.Next()
	}
}

// Logging 日志中间件
func Logging() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] %s %s %d %s %s\n",
			param.TimeStamp.Format("2006-01-02 15:04:05"),
			param.Method,
			param.Path,
			param.StatusCode,
			param.Latency,
			param.ClientIP,
		)
	})
}

// Recovery 恢复中间件
func Recovery() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		if err, ok := recovered.(string); ok {
			// 记录错误日志
			fmt.Printf("Panic recovered: %s\n", err)
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "服务器内部错误",
		})
	})
}
