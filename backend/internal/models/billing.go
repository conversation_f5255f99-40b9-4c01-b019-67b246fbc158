package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Transaction 财务交易模型
type Transaction struct {
	ID                string          `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID            string          `json:"user_id" gorm:"type:uuid;not null"`
	ResearchGroupID   string          `json:"research_group_id" gorm:"type:uuid;not null"`
	Type              TransactionType `json:"type" gorm:"type:varchar(20);not null"`
	Amount            float64         `json:"amount" gorm:"type:decimal(10,2);not null"`
	Description       string          `json:"description" gorm:"not null"`
	RelatedReservationID *string      `json:"related_reservation_id,omitempty" gorm:"type:uuid"`
	RelatedInstrumentID  *string      `json:"related_instrument_id,omitempty" gorm:"type:uuid"`
	Status            PaymentStatus   `json:"status" gorm:"type:varchar(20);not null;default:'pending'"`
	TransactionDate   time.Time       `json:"transaction_date" gorm:"not null"`
	PaymentMethod     *string         `json:"payment_method,omitempty" gorm:"type:varchar(50)"`
	TransactionID     *string         `json:"transaction_id,omitempty" gorm:"type:varchar(100)"` // 第三方交易ID
	CreatedAt         time.Time       `json:"created_at"`
	UpdatedAt         time.Time       `json:"updated_at"`

	// 关联
	User              User            `json:"user" gorm:"foreignKey:UserID"`
	ResearchGroup     ResearchGroup   `json:"research_group" gorm:"foreignKey:ResearchGroupID"`
	Reservation       *Reservation    `json:"reservation,omitempty" gorm:"foreignKey:RelatedReservationID"`
	Instrument        *Instrument     `json:"instrument,omitempty" gorm:"foreignKey:RelatedInstrumentID"`
}

// TransactionType 交易类型枚举
type TransactionType string

const (
	TransactionTypeCharge    TransactionType = "charge"
	TransactionTypeRefund    TransactionType = "refund"
	TransactionTypeDeposit   TransactionType = "deposit"
	TransactionTypeAdjustment TransactionType = "adjustment"
)

// PaymentStatus 支付状态枚举
type PaymentStatus string

const (
	PaymentStatusPending   PaymentStatus = "pending"
	PaymentStatusCompleted PaymentStatus = "completed"
	PaymentStatusFailed    PaymentStatus = "failed"
	PaymentStatusCancelled PaymentStatus = "cancelled"
	PaymentStatusRefunded  PaymentStatus = "refunded"
)

// Bill 账单模型
type Bill struct {
	ID              string      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	ResearchGroupID string      `json:"research_group_id" gorm:"type:uuid;not null"`
	Title           string      `json:"title" gorm:"not null"`
	Description     string      `json:"description" gorm:"not null"`
	TotalAmount     float64     `json:"total_amount" gorm:"type:decimal(10,2);not null"`
	Status          BillStatus  `json:"status" gorm:"type:varchar(20);not null;default:'unpaid'"`
	BillDate        time.Time   `json:"bill_date" gorm:"not null"`
	DueDate         *time.Time  `json:"due_date,omitempty"`
	PaidAt          *time.Time  `json:"paid_at,omitempty"`
	CreatedAt       time.Time   `json:"created_at"`
	UpdatedAt       time.Time   `json:"updated_at"`

	// 关联
	ResearchGroup   ResearchGroup `json:"research_group" gorm:"foreignKey:ResearchGroupID"`
	Items           []BillItem     `json:"items,omitempty" gorm:"foreignKey:BillID"`
}

// BillStatus 账单状态枚举
type BillStatus string

const (
	BillStatusUnpaid     BillStatus = "unpaid"
	BillStatusPaid       BillStatus = "paid"
	BillStatusOverdue    BillStatus = "overdue"
	BillStatusCancelled  BillStatus = "cancelled"
)

// BillItem 账单项目
type BillItem struct {
	ID            string    `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	BillID        string    `json:"bill_id" gorm:"type:uuid;not null"`
	InstrumentName string   `json:"instrument_name" gorm:"not null"`
	ReservationID string    `json:"reservation_id" gorm:"type:uuid;not null"`
	UsageDate     time.Time `json:"usage_date" gorm:"not null"`
	Duration      float64   `json:"duration" gorm:"type:decimal(8,2);not null"` // 使用时长（小时）
	UnitPrice     float64   `json:"unit_price" gorm:"type:decimal(10,2);not null"`
	Quantity      float64   `json:"quantity" gorm:"type:decimal(10,2);not null"` // 数量（样品数或小时数）
	Amount        float64   `json:"amount" gorm:"type:decimal(10,2);not null"`
	Discount      float64   `json:"discount" gorm:"type:decimal(3,2);default:0"` // 折扣比例 0-1
	Notes         *string   `json:"notes,omitempty" gorm:"type:text"`

	// 关联
	Bill          Bill      `json:"bill" gorm:"foreignKey:BillID"`
	Reservation   Reservation `json:"reservation" gorm:"foreignKey:ReservationID"`
}

// DepositRecord 充值记录模型
type DepositRecord struct {
	ID              string        `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	ResearchGroupID string        `json:"research_group_id" gorm:"type:uuid;not null"`
	OperatorID      string        `json:"operator_id" gorm:"type:uuid;not null"`
	Amount          float64       `json:"amount" gorm:"type:decimal(10,2);not null"`
	PaymentMethod   string        `json:"payment_method" gorm:"type:varchar(50);not null"`
	TransactionID   *string       `json:"transaction_id,omitempty" gorm:"type:varchar(100)"`
	Status          DepositStatus `json:"status" gorm:"type:varchar(20);not null;default:'pending'"`
	Notes           *string       `json:"notes,omitempty" gorm:"type:text"`
	DepositDate     time.Time     `json:"deposit_date" gorm:"not null"`
	ProcessedAt     *time.Time    `json:"processed_at,omitempty"`
	CreatedAt       time.Time     `json:"created_at"`
	UpdatedAt       time.Time     `json:"updated_at"`

	// 关联
	ResearchGroup   ResearchGroup `json:"research_group" gorm:"foreignKey:ResearchGroupID"`
	Operator        User          `json:"operator" gorm:"foreignKey:OperatorID"`
}

// DepositStatus 充值状态枚举
type DepositStatus string

const (
	DepositStatusPending   DepositStatus = "pending"
	DepositStatusCompleted DepositStatus = "completed"
	DepositStatusFailed    DepositStatus = "failed"
	DepositStatusCancelled DepositStatus = "cancelled"
)

// FinancialStats 财务统计模型
type FinancialStats struct {
	TotalRevenue        float64               `json:"total_revenue"`
	MonthlyRevenue      float64               `json:"monthly_revenue"`
	PendingPayments     float64               `json:"pending_payments"`
	TotalTransactions   int                   `json:"total_transactions"`
	RevenueByInstrument map[string]float64    `json:"revenue_by_instrument"`
	RevenueByUserType   map[string]float64    `json:"revenue_by_user_type"`
	MonthlyTrend        []MonthlyRevenue      `json:"monthly_trend"`
}

// MonthlyRevenue 月度收入数据
type MonthlyRevenue struct {
	Month           string  `json:"month"` // YYYY-MM格式
	Revenue         float64 `json:"revenue"`
	TransactionCount int    `json:"transaction_count"`
}

// BeforeCreate GORM钩子：在创建前生成UUID
func (t *Transaction) BeforeCreate(tx *gorm.DB) error {
	if t.ID == "" {
		t.ID = uuid.New().String()
	}
	return nil
}

func (b *Bill) BeforeCreate(tx *gorm.DB) error {
	if b.ID == "" {
		b.ID = uuid.New().String()
	}
	return nil
}

func (bi *BillItem) BeforeCreate(tx *gorm.DB) error {
	if bi.ID == "" {
		bi.ID = uuid.New().String()
	}
	return nil
}

func (dr *DepositRecord) BeforeCreate(tx *gorm.DB) error {
	if dr.ID == "" {
		dr.ID = uuid.New().String()
	}
	return nil
}

// TableName 指定表名
func (Transaction) TableName() string {
	return "transactions"
}

func (Bill) TableName() string {
	return "bills"
}

func (BillItem) TableName() string {
	return "bill_items"
}

func (DepositRecord) TableName() string {
	return "deposit_records"
}










