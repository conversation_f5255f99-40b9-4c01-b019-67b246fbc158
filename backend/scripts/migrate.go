package main

import (
	"database/sql"
	"fmt"
	"io/ioutil"
	"log"
	"path/filepath"
	"sort"
	"strings"

	"github.com/lib/pq"
	"github.com/spf13/viper"
)

// Migration 迁移文件结构
type Migration struct {
	Version string
	Name    string
	UpSQL   string
	DownSQL string
}

// MigrationManager 迁移管理器
type MigrationManager struct {
	db *sql.DB
}

// NewMigrationManager 创建迁移管理器
func NewMigrationManager(db *sql.DB) *MigrationManager {
	return &MigrationManager{db: db}
}

// Init 初始化迁移表
func (m *MigrationManager) Init() error {
	_, err := m.db.Exec(`
		CREATE TABLE IF NOT EXISTS schema_migrations (
			version VARCHAR(255) PRIMARY KEY,
			name VARCHAR(255) NOT NULL,
			applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
		)
	`)
	return err
}

// GetAppliedMigrations 获取已应用的迁移
func (m *MigrationManager) GetAppliedMigrations() (map[string]bool, error) {
	rows, err := m.db.Query("SELECT version FROM schema_migrations ORDER BY version")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	applied := make(map[string]bool)
	for rows.Next() {
		var version string
		if err := rows.Scan(&version); err != nil {
			return nil, err
		}
		applied[version] = true
	}
	return applied, nil
}

// ApplyMigration 应用迁移
func (m *MigrationManager) ApplyMigration(migration *Migration) error {
	tx, err := m.db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// 执行迁移SQL
	if _, err := tx.Exec(migration.UpSQL); err != nil {
		return fmt.Errorf("执行迁移失败 %s: %w", migration.Version, err)
	}

	// 记录迁移
	if _, err := tx.Exec(
		"INSERT INTO schema_migrations (version, name) VALUES ($1, $2)",
		migration.Version, migration.Name,
	); err != nil {
		return fmt.Errorf("记录迁移失败 %s: %w", migration.Version, err)
	}

	return tx.Commit()
}

// RollbackMigration 回滚迁移
func (m *MigrationManager) RollbackMigration(migration *Migration) error {
	tx, err := m.db.Begin()
	if err != nil {
		return err
	}
	defer tx.Rollback()

	// 执行回滚SQL
	if _, err := tx.Exec(migration.DownSQL); err != nil {
		return fmt.Errorf("执行回滚失败 %s: %w", migration.Version, err)
	}

	// 删除迁移记录
	if _, err := tx.Exec(
		"DELETE FROM schema_migrations WHERE version = $1",
		migration.Version,
	); err != nil {
		return fmt.Errorf("删除迁移记录失败 %s: %w", migration.Version, err)
	}

	return tx.Commit()
}

// LoadMigrations 加载迁移文件
func LoadMigrations(migrationsDir string) ([]*Migration, error) {
	files, err := ioutil.ReadDir(migrationsDir)
	if err != nil {
		return nil, err
	}

	migrations := make(map[string]*Migration)

	for _, file := range files {
		if file.IsDir() {
			continue
		}

		filename := file.Name()
		if !strings.HasSuffix(filename, ".up.sql") && !strings.HasSuffix(filename, ".down.sql") {
			continue
		}

		parts := strings.Split(filename, "_")
		if len(parts) < 3 {
			continue
		}

		version := parts[0]
		isUp := strings.HasSuffix(filename, ".up.sql")
		isDown := strings.HasSuffix(filename, ".down.sql")

		if !isUp && !isDown {
			continue
		}

		name := strings.Join(parts[1:len(parts)-1], "_")

		content, err := ioutil.ReadFile(filepath.Join(migrationsDir, filename))
		if err != nil {
			return nil, err
		}

		if migrations[version] == nil {
			migrations[version] = &Migration{
				Version: version,
				Name:    name,
			}
		}

		if isUp {
			migrations[version].UpSQL = string(content)
		} else {
			migrations[version].DownSQL = string(content)
		}
	}

	// 转换为切片并排序
	var result []*Migration
	for _, migration := range migrations {
		if migration.UpSQL == "" {
			return nil, fmt.Errorf("迁移文件不完整: %s (缺少.up.sql文件)", migration.Version)
		}
		if migration.DownSQL == "" {
			return nil, fmt.Errorf("迁移文件不完整: %s (缺少.down.sql文件)", migration.Version)
		}
		result = append(result, migration)
	}

	sort.Slice(result, func(i, j int) bool {
		return result[i].Version < result[j].Version
	})

	return result, nil
}

// buildDSN 构建数据库连接字符串
func buildDSN() string {
	host := viper.GetString("database.host")
	port := viper.GetInt("database.port")
	username := viper.GetString("database.username")
	password := viper.GetString("database.password")
	database := viper.GetString("database.database")
	sslMode := viper.GetString("database.ssl_mode")

	return fmt.Sprintf(
		"host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		host, port, username, password, database, sslMode,
	)
}

func main() {
	// 初始化配置
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")
	viper.AddConfigPath("../")

	if err := viper.ReadInConfig(); err != nil {
		log.Fatalf("读取配置文件失败: %v", err)
	}

	// 连接数据库
	db, err := sql.Open("postgres", buildDSN())
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	if err := db.Ping(); err != nil {
		log.Fatalf("数据库连接测试失败: %v", err)
	}

	// 创建迁移管理器
	manager := NewMigrationManager(db)

	// 初始化迁移表
	if err := manager.Init(); err != nil {
		log.Fatalf("初始化迁移表失败: %v", err)
	}

	// 加载迁移文件
	migrations, err := LoadMigrations("../migrations")
	if err != nil {
		log.Fatalf("加载迁移文件失败: %v", err)
	}

	// 获取已应用的迁移
	applied, err := manager.GetAppliedMigrations()
	if err != nil {
		log.Fatalf("获取已应用迁移失败: %v", err)
	}

	// 应用未应用的迁移
	for _, migration := range migrations {
		if !applied[migration.Version] {
			fmt.Printf("应用迁移: %s - %s\n", migration.Version, migration.Name)
			if err := manager.ApplyMigration(migration); err != nil {
				log.Fatalf("应用迁移失败: %v", err)
			}
			fmt.Printf("✓ 迁移 %s 应用成功\n", migration.Version)
		} else {
			fmt.Printf("跳过已应用的迁移: %s - %s\n", migration.Version, migration.Name)
		}
	}

	fmt.Println("数据库迁移完成!")
}













