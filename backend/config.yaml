# 应用程序配置
app:
  name: "西北工业大学大型仪器共享管理系统"
  version: "1.0.0"
  env: "development"  # development, production
  timezone: "Asia/Shanghai"

# 服务器配置
server:
  host: "localhost"
  port: "8080"
  read_timeout: 30
  write_timeout: 30
  max_header_bytes: 1048576

# 数据库配置
database:
  host: "localhost"
  port: 5432
  username: "postgres"
  password: "password"
  database: "instrument_management"
  ssl_mode: "disable"
  max_open_conns: 100
  max_idle_conns: 10
  conn_max_lifetime: 3600

# JWT配置
jwt:
  secret: "your-super-secret-jwt-key-change-in-production"
  expire_hours: 24
  refresh_expire_hours: 168  # 7天

# 日志配置
log:
  level: "info"  # debug, info, warn, error
  format: "json"  # json, text
  output: "stdout"  # stdout, file
  file_path: "logs/app.log"

# 文件上传配置
upload:
  max_size: 10485760  # 10MB
  allowed_types: [".jpg", ".jpeg", ".png", ".pdf", ".doc", ".docx", ".xls", ".xlsx"]
  base_path: "uploads"

# 邮件配置
email:
  smtp_host: "smtp.qq.com"
  smtp_port: 587
  username: "<EMAIL>"
  password: "your-email-password"
  from_name: "西北工业大学仪器共享平台"

# 缓存配置
cache:
  redis_host: "localhost"
  redis_port: 6379
  redis_password: ""
  redis_db: 0
  expire_time: 3600

# 定时任务配置
cron:
  # 每天凌晨2点清理过期预约
  cleanup_expired_reservations: "0 2 * * *"
  # 每月1号生成财务报表
  generate_monthly_report: "0 0 1 * *"
  # 每小时检查仪器状态
  check_instrument_status: "0 * * * *"

# 第三方服务配置
third_party:
  # 统一身份认证
  sso:
    base_url: "https://sso.nwpu.edu.cn"
    client_id: "instrument_management"
    client_secret: "your-client-secret"

  # 支付服务
  payment:
    alipay_app_id: "your-alipay-app-id"
    alipay_private_key: "your-alipay-private-key"
    wechat_app_id: "your-wechat-app-id"
    wechat_mch_id: "your-wechat-mch-id"
    wechat_private_key: "your-wechat-private-key"

# 业务配置
business:
  # 默认工作时间
  default_working_hours:
    start: "09:00"
    end: "17:00"
    weekdays: [1, 2, 3, 4, 5]  # 周一到周五

  # 预约配置
  reservation:
    max_advance_days: 30  # 最多提前预约天数
    min_duration_minutes: 30  # 最短预约时长（分钟）
    max_duration_hours: 8  # 最长预约时长（小时）
    auto_cancel_minutes: 15  # 预约开始前多少分钟自动取消未确认预约

  # 计费配置
  billing:
    currency: "CNY"
    precision: 2  # 小数点精度
    auto_billing_enabled: true
    payment_deadline_days: 30  # 账单支付期限

  # 培训配置
  training:
    validity_months: 12  # 培训有效期（月）
    auto_expiry_check: true

  # 离线配置
  offline:
    password_validity_hours: 24  # 离线密码有效期
    sync_interval_minutes: 5  # 同步间隔













