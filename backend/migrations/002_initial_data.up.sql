-- 插入基础数据
-- 创建时间: 2024-01-15

-- 插入管理员用户
INSERT INTO users (id, username, email, phone, password_hash, role, status, full_name, department, created_at, updated_at)
VALUES
    ('550e8400-e29b-41d4-a716-446655440000', 'admin', '<EMAIL>', '13800138000',
     '$2a$10$N9qo8uLOickgx2ZMRZoMyeI5Ht4QGJzMtgE5Lx9QK3v/K9U3WJzQG', -- 密码: admin123
     'admin', 'active', '系统管理员', '信息技术中心', NOW(), NOW());

-- 插入教师用户
INSERT INTO users (id, username, email, phone, password_hash, role, status, full_name, department, created_at, updated_at)
VALUES
    ('550e8400-e29b-41d4-a716-446655440001', 'teacher1', '<EMAIL>', '13800138001',
     '$2a$10$N9qo8uLOickgx2ZMRZoMyeI5Ht4QGJzMtgE5Lx9QK3v/K9U3WJzQG', -- 密码: admin123
     'teacher', 'active', '张教授', '材料学院', NOW(), NOW()),
    ('550e8400-e29b-41d4-a716-446655440002', 'teacher2', '<EMAIL>', '13800138002',
     '$2a$10$N9qo8uLOickgx2ZMRZoMyeI5Ht4QGJzMtgE5Lx9QK3v/K9U3WJzQG', -- 密码: admin123
     'teacher', 'active', '李教授', '物理学院', NOW(), NOW());

-- 插入仪器负责人用户
INSERT INTO users (id, username, email, phone, password_hash, role, status, full_name, department, created_at, updated_at)
VALUES
    ('550e8400-e29b-41d4-a716-446655440003', 'technician1', '<EMAIL>', '13800138003',
     '$2a$10$N9qo8uLOickgx2ZMRZoMyeI5Ht4QGJzMtgE5Lx9QK3v/K9U3WJzQG', -- 密码: admin123
     'technician', 'active', '王技术员', '材料学院', NOW(), NOW()),
    ('550e8400-e29b-41d4-a716-446655440004', 'technician2', '<EMAIL>', '13800138004',
     '$2a$10$N9qo8uLOickgx2ZMRZoMyeI5Ht4QGJzMtgE5Lx9QK3v/K9U3WJzQG', -- 密码: admin123
     'technician', 'active', '赵技术员', '物理学院', NOW(), NOW());

-- 插入课题组
INSERT INTO research_groups (id, name, description, leader_id, balance, created_at, updated_at)
VALUES
    ('660e8400-e29b-41d4-a716-446655440000', '材料科学课题组', '专注于材料科学研究的课题组', '550e8400-e29b-41d4-a716-446655440001', 10000.00, NOW(), NOW()),
    ('660e8400-e29b-41d4-a716-446655440001', '物理学课题组', '物理学基础研究的课题组', '550e8400-e29b-41d4-a716-446655440002', 15000.00, NOW(), NOW());

-- 插入仪器
INSERT INTO instruments (id, name, description, model, manufacturer, location, status, control_type, requires_training, accepts_sample_submission, requires_reservation, responsible_user_id, created_at, updated_at)
VALUES
    ('770e8400-e29b-41d4-a716-446655440000', '扫描电子显微镜', '高分辨率扫描电子显微镜，用于材料微观结构分析', 'SEM-1000', 'NPU Instruments', '理学院B101', 'online', 'computer', true, false, true, '550e8400-e29b-41d4-a716-446655440003', NOW(), NOW()),
    ('770e8400-e29b-41d4-a716-446655440001', 'X射线衍射仪', '用于晶体结构分析的X射线衍射仪', 'XRD-2000', 'NPU Instruments', '材料学院A203', 'online', 'automated', true, true, true, '550e8400-e29b-41d4-a716-446655440003', NOW(), NOW()),
    ('770e8400-e29b-41d4-a716-446655440002', '原子力显微镜', '纳米级表面形貌分析仪器', 'AFM-300', 'NPU Instruments', '物理学院C105', 'maintenance', 'manual', false, false, true, '550e8400-e29b-41d4-a716-446655440004', NOW(), NOW()),
    ('770e8400-e29b-41d4-a716-446655440003', '激光共聚焦显微镜', '用于生物样品三维成像的高分辨率显微镜', 'LSM-800', 'Zeiss', '生命学院D201', 'online', 'computer', true, false, true, '550e8400-e29b-41d4-a716-446655440004', NOW(), NOW()),
    ('770e8400-e29b-41d4-a716-446655440004', '核磁共振谱仪', '用于分子结构分析的高场核磁共振谱仪', 'NMR-600', 'Bruker', '化学学院E301', 'online', 'automated', true, true, true, '550e8400-e29b-41d4-a716-446655440003', NOW(), NOW());

-- 插入工作时间配置
INSERT INTO working_hours (instrument_id, day_of_week, start_time, end_time, is_working_day)
VALUES
    -- 扫描电子显微镜的工作时间（周一到周五）
    ('770e8400-e29b-41d4-a716-446655440000', 1, '09:00', '17:00', true),
    ('770e8400-e29b-41d4-a716-446655440000', 2, '09:00', '17:00', true),
    ('770e8400-e29b-41d4-a716-446655440000', 3, '09:00', '17:00', true),
    ('770e8400-e29b-41d4-a716-446655440000', 4, '09:00', '17:00', true),
    ('770e8400-e29b-41d4-a716-446655440000', 5, '09:00', '17:00', true),
    ('770e8400-e29b-41d4-a716-446655440000', 6, '09:00', '12:00', false),
    ('770e8400-e29b-41d4-a716-446655440000', 7, '09:00', '12:00', false),

    -- X射线衍射仪的工作时间（周一到周六）
    ('770e8400-e29b-41d4-a716-446655440001', 1, '08:00', '20:00', true),
    ('770e8400-e29b-41d4-a716-446655440001', 2, '08:00', '20:00', true),
    ('770e8400-e29b-41d4-a716-446655440001', 3, '08:00', '20:00', true),
    ('770e8400-e29b-41d4-a716-446655440001', 4, '08:00', '20:00', true),
    ('770e8400-e29b-41d4-a716-446655440001', 5, '08:00', '20:00', true),
    ('770e8400-e29b-41d4-a716-446655440001', 6, '09:00', '17:00', true),
    ('770e8400-e29b-41d4-a716-446655440001', 7, '09:00', '12:00', false);

-- 插入计费配置
INSERT INTO billing_configs (instrument_id, billing_type, base_rate, unit, tag_discounts)
VALUES
    ('770e8400-e29b-41d4-a716-446655440000', 'per_hour', 50.00, '元/小时', '{"院内用户": 0.8, "校内用户": 0.9}'),
    ('770e8400-e29b-41d4-a716-446655440001', 'per_sample', 200.00, '元/样品', '{"院内用户": 0.7, "校内用户": 0.8}'),
    ('770e8400-e29b-41d4-a716-446655440002', 'per_hour', 30.00, '元/小时', '{"院内用户": 0.9, "校内用户": 0.95}'),
    ('770e8400-e29b-41d4-a716-446655440003', 'per_hour', 80.00, '元/小时', '{"院内用户": 0.8, "校内用户": 0.9}'),
    ('770e8400-e29b-41d4-a716-446655440004', 'per_hour', 100.00, '元/小时', '{"院内用户": 0.6, "校内用户": 0.8}');

-- 插入用户标签
INSERT INTO user_tags (instrument_id, name, description, priority, discount)
VALUES
    ('770e8400-e29b-41d4-a716-446655440000', '院内用户', '本学院用户', 0.8, 0.8),
    ('770e8400-e29b-41d4-a716-446655440000', '校内用户', '本校其他学院用户', 0.6, 0.9),
    ('770e8400-e29b-41d4-a716-446655440001', '院内用户', '本学院用户', 0.8, 0.7),
    ('770e8400-e29b-41d4-a716-446655440001', '校内用户', '本校其他学院用户', 0.6, 0.8);













