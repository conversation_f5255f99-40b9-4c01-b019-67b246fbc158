-- 回滚初始化数据库表结构
-- 创建时间: 2024-01-15

-- 删除触发器
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
DROP TRIGGER IF EXISTS update_research_groups_updated_at ON research_groups;
DROP TRIGGER IF EXISTS update_instruments_updated_at ON instruments;
DROP TRIGGER IF EXISTS update_reservations_updated_at ON reservations;
DROP TRIGGER IF EXISTS update_usage_records_updated_at ON usage_records;
DROP TRIGGER IF EXISTS update_training_records_updated_at ON training_records;
DROP TRIGGER IF EXISTS update_transactions_updated_at ON transactions;
DROP TRIGGER IF EXISTS update_bills_updated_at ON bills;
DROP TRIGGER IF EXISTS update_deposit_records_updated_at ON deposit_records;

-- 删除触发器函数
DROP FUNCTION IF EXISTS update_updated_at_column();

-- 删除表（注意删除顺序，要先删除有外键引用的表）
DROP TABLE IF EXISTS deposit_records;
DROP TABLE IF EXISTS bill_items;
DROP TABLE IF EXISTS bills;
DROP TABLE IF EXISTS transactions;
DROP TABLE IF EXISTS training_records;
DROP TABLE IF EXISTS usage_records;
DROP TABLE IF EXISTS reservations;
DROP TABLE IF EXISTS user_tags;
DROP TABLE IF EXISTS billing_configs;
DROP TABLE IF EXISTS working_hours;
DROP TABLE IF EXISTS instruments;
DROP TABLE IF EXISTS research_groups;
DROP TABLE IF EXISTS users;

-- 删除UUID扩展（如果需要的话）
-- DROP EXTENSION IF EXISTS "uuid-ossp";













