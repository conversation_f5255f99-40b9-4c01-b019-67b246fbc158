#!/usr/bin/env python3
import sys
import os
sys.path.append('/usr/local/lib/python3.9/site-packages')

try:
    import PyPDF2
    pdf_path = '/Users/<USER>/Documents/company/zzxwl/management-system/docs/西北工业大学大型仪器共享管理系统使用手册.pdf'

    if os.path.exists(pdf_path):
        pdf = PyPDF2.PdfReader(pdf_path)
        print(f"PDF总页数: {len(pdf.pages)}")

        text = ''
        for page in pdf.pages:
            text += page.extract_text() + '\n'

        print("\n=== PDF内容摘要 ===")
        print(text[:3000])  # 只显示前3000字符
        print("\n=== 内容结束 ===")
    else:
        print(f"PDF文件不存在: {pdf_path}")

except ImportError as e:
    print(f"无法导入PyPDF2: {e}")
    print("尝试安装: pip install PyPDF2")
except Exception as e:
    print(f"读取PDF时出错: {e}")